{"workbench.colorCustomizations": {"activityBar.activeBackground": "#001b80", "activityBar.background": "#001b80", "activityBar.foreground": "#e7e7e7", "activityBar.inactiveForeground": "#e7e7e799", "activityBarBadge.background": "#bf0028", "activityBarBadge.foreground": "#e7e7e7", "commandCenter.border": "#e7e7e799", "sash.hoverBorder": "#001b80", "statusBar.background": "#00104d", "statusBar.foreground": "#e7e7e7", "statusBarItem.hoverBackground": "#001b80", "statusBarItem.remoteBackground": "#00104d", "statusBarItem.remoteForeground": "#e7e7e7", "titleBar.activeBackground": "#00104d", "titleBar.activeForeground": "#e7e7e7", "titleBar.inactiveBackground": "#00104d99", "titleBar.inactiveForeground": "#e7e7e799"}, "peacock.color": "#00104d", "cSpell.words": ["actionsheet", "ahooks", "aibook", "aini", "AIQA", "aiservices", "appconfservice", "applinks", "appn", "appt", "appv", "arithmeticpk", "arrowright", "<PERSON><PERSON><PERSON><PERSON>", "bizboot", "biz<PERSON><PERSON><PERSON>", "Bookln", "booklnboot", "bule", "buyu", "chen<PERSON>", "chromeless", "dimezis", "dtos", "ellipsize", "exercisereport", "gorhom", "gulu", "iconsax", "iflytek", "imds", "imserver", "itechserver", "iwatch", "jsamr", "judgecountdown", "judgeright", "kousuanlianxi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leftcrown", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "listtypeandgrade", "matchinghead", "mathexam", "mediaresourceservice", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mmkv", "modalbox", "netinfo", "oralarithmetic", "overdrag", "<PERSON><PERSON>", "pkdefeat", "pkdraw", "PKDTO", "pkplay", "pksucceed", "Prepub", "Pressable", "qrcode", "querybykey", "Reactotron", "readygo", "redash", "<PERSON><PERSON><PERSON>", "rightanswers", "rightcrown", "rnblurview", "RNFS", "socketservertcp", "sonner", "Srts", "styleable", "Subviews", "tamagui", "tarojs", "templateid", "translateservice", "<PERSON><PERSON>", "unsynthesized", "vsyellow", "<PERSON><PERSON><PERSON>", "wechat", "weixin", "wxmp", "xing<PERSON>g", "xuebihua", "xuepianpang", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yunti", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.formatOnSave": true, "tailwindCSS.classAttributes": ["className", "jglClassName"], "editor.codeActionsOnSave": {"source.fixAll.eslint": "always"}}