import { BooklnLoginHooks } from '@bookln/bookln-biz';
import { useActionSheet } from '@expo/react-native-action-sheet';
import {
  isMemoryLoggerEnabledAtom,
  isNetworkLoggingEnabledAtom,
  storageKeys,
  useAppSelector,
} from '@jgl/biz-func';
import { container } from '@jgl/container';
import {
  envVars,
  router,
  showToast,
  storage,
  useDidShow,
  useStorage,
} from '@jgl/utils';
import { Env } from '@yunti-private/env';
import { ExpoUpdateReleaseChannel } from '@yunti-private/rn-expo-updates-helper';
import { MemoryLogger } from '@yunti-private/rn-memory-logger';
import * as Clipboard from 'expo-clipboard';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import * as Updates from 'expo-updates';
import { useAtom, useAtomValue, useSetAtom } from 'jotai';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { Alert, Platform } from 'react-native';
import { useToast } from 'react-native-toast-hybrid';
import { But<PERSON>, ScrollView, Switch, Text, XStack, YStack } from 'tamagui';
import { atomMap, currentChannelAtom } from '../../atom';
import { ALL_CHANNELS } from '../../constants/BizConfig';
import { useExpoUpdate } from '../../hooks/sdk/useUpdateConfig';
import { useLogIn } from '../../hooks/useLogIn';
import { useStartNetworkLogging } from '../../utils/networkLogUtil';

const { useLogout } = BooklnLoginHooks;

const envNames = [
  { name: 'daily', env: Env.Daily },
  { name: 'prepub', env: Env.Prepub },
  { name: 'production', env: Env.Production },
];

const updateReleaseChannelNames = [
  { name: 'staging', channel: ExpoUpdateReleaseChannel.Staging },
  { name: 'production', channel: ExpoUpdateReleaseChannel.Production },
];

const useDebugSettingsScreen = () => {
  const userInfo = useAppSelector((s) => s.userInfo);
  const userInfoString = JSON.stringify(userInfo, undefined, 2);
  const toast = useToast();
  const [updateConfig] = useAtom(atomMap.updateConfigAtom);

  const appBuildTime =
    envVars.appBuildTime?.() === 'undefined'
      ? undefined
      : envVars.appBuildTime?.();
  const appBuildBranch =
    envVars.appBuildBranch?.() === 'undefined'
      ? undefined
      : envVars.appBuildBranch?.();
  const appBuildCommit =
    envVars.appBuildCommit?.() === 'undefined'
      ? undefined
      : envVars.appBuildCommit?.();

  const appNativeBuildTime =
    envVars.appNativeBuildTime?.() === 'undefined'
      ? undefined
      : envVars.appNativeBuildTime?.();
  const appNativeBuildBranch =
    envVars.appNativeBuildBranch?.() === 'undefined'
      ? undefined
      : envVars.appNativeBuildBranch?.();
  const appNativeBuildCommit =
    envVars.appNativeBuildCommit?.() === 'undefined'
      ? undefined
      : envVars.appNativeBuildCommit?.();

  const appBuildInfo = {
    appBuildTime,
    appBuildBranch,
    appBuildCommit,
  };

  const appNativeBuildInfo = {
    appNativeBuildTime,
    appNativeBuildBranch,
    appNativeBuildCommit,
  };

  const reduxState = useAppSelector((s) => s);
  const {
    initUpdateConfig: getUpdateConfig,
    config: expoUpdateConfig,
    updateInfoOnLaunch,
    manualCheckForUpdate,
  } = useExpoUpdate();
  const updateReleaseChannel = useMemo(
    () => expoUpdateConfig?.releaseChannel,
    [expoUpdateConfig?.releaseChannel],
  );

  const [
    isStartingNetworkLoggingOnLaunchEnabled,
    setIsStartingNetworkLoggingOnLaunchEnabled,
  ] = useState<boolean>(false);
  const [
    isStartingMemoryLoggerOnLaunchEnabled,
    setIsStartingMemoryLoggerOnLaunchEnabled,
  ] = useState<boolean>(true);
  const {
    setItem: setStorageEnableNetworkLogOnLaunch,
    getItem: getStorageEnableNetworkLogOnLaunch,
  } = useStorage(storageKeys.enableNetworkLogOnLaunch, { env: false });
  const {
    setItem: setStorageEnableMemoryLogOnLaunch,
    getItem: getStorageEnableMemoryLogOnLaunch,
  } = useStorage(storageKeys.enableMemoryLogOnLaunch, { env: false });

  const { startNetworkLogging } = useStartNetworkLogging();
  const isNetworkLoggingEnabled = useAtomValue(isNetworkLoggingEnabledAtom);
  const [isMemoryLoggerEnabled, setIsMemoryLoggerEnabled] = useAtom(
    isMemoryLoggerEnabledAtom,
  );

  useDidShow(() => {
    if (!expoUpdateConfig) {
      getUpdateConfig();
    }
  });

  useEffect(() => {
    getStorageEnableNetworkLogOnLaunch().then((value) => {
      const enabled = value === 'true';
      setIsStartingNetworkLoggingOnLaunchEnabled(enabled);
    });
  }, [getStorageEnableNetworkLogOnLaunch]);

  useEffect(() => {
    getStorageEnableMemoryLogOnLaunch().then((value) => {
      // TODO: leejunhui - 为了定位浏览模式下的报错，所以有 !value (2025_05_14)
      const enabled = value === 'true' || !value;
      setIsStartingMemoryLoggerOnLaunchEnabled(enabled);
    });
  }, [getStorageEnableMemoryLogOnLaunch]);

  // 测试 Sentry 事件上报
  // TODO: leejunhui - 测试 Sentry 事件上报 (2025_03_06)
  // useEffect(() => {
  //   captureEvent({
  //     message: '测试 Sentry 事件上报 - 进入了调试选项页',
  //     level: 'debug',
  //     tags: {
  //       category: 'expo-updates',
  //     },
  //     extra: {
  //       user_id: userInfo.userId,
  //       user_name: userInfo.name,
  //       env: container.env().env(),
  //       app_version: getVersion(),
  //     },
  //   });
  // }, [userInfo.name, userInfo.userId]);

  const handlePressViewAsyncStorage = useCallback(async () => {
    const keysAndValues = [];
    const keys = await storage.getAllKeys();
    for await (const key of keys) {
      const value = await storage.getItem(key);
      keysAndValues.push({ [key]: value });
    }
    router.push('/debugInfo', {
      title: 'Async Storage',
      info: JSON.stringify(keysAndValues, undefined, 2),
    });
  }, []);

  const handlePressClearAsyncStorage = useCallback(async () => {
    await storage.clear();
    showToast({ title: '清除完成' });
  }, []);

  const handlePressViewRedux = useCallback(async () => {
    router.push('/debugInfo', {
      title: 'Redux',
      info: JSON.stringify(reduxState, undefined, 2),
    });
  }, [reduxState]);

  const handlePressTamagui = useCallback(async () => {
    router.push('/debugTamagui');
  }, []);

  const handlePressJglUIV4 = useCallback(async () => {
    router.push('/debugJglUIV4');
  }, []);

  const handlePressCrossPlatformComponents = useCallback(async () => {
    router.push('/debugCrossPlatformComponents');
  }, []);

  const handlePressSkiaDemo = useCallback(async () => {
    router.push('/debugSkia');
  }, []);

  const handlePressSectionListDemo = useCallback(async () => {
    router.push('/debugSectionList');
  }, []);

  const handlePressRNViewsDemo = useCallback(async () => {
    router.push('/debugRNViews');
  }, []);

  const handlePressPrivateJglUI = useCallback(async () => {
    router.push('/debugPrivateJglUI');
  }, []);

  const handlePressViewNet = useCallback(async () => {
    if (isNetworkLoggingEnabled) {
      router.push('/debugNet');
    } else {
      startNetworkLogging();
      showToast({ title: '网络请求log已开启' });
    }
  }, [isNetworkLoggingEnabled, startNetworkLogging]);

  const handlePressViewMemoryLogger = useCallback(async () => {
    if (isMemoryLoggerEnabled) {
      router.push('/debugMemoryLogger');
    } else {
      setIsMemoryLoggerEnabled(true);
      showToast({ title: '内存日志已开启' });
    }
  }, [isMemoryLoggerEnabled, setIsMemoryLoggerEnabled]);

  const handlePressUserInfo = useCallback(async () => {
    await Clipboard.setStringAsync(userInfoString);
    showToast({ title: '用户信息已复制' });
  }, [userInfoString]);

  const expoUpdateConfigStr = useMemo(() => {
    return JSON.stringify(expoUpdateConfig, undefined, 2);
  }, [expoUpdateConfig]);

  const handlePressExpoUpdateConfig = useCallback(async () => {
    await Clipboard.setStringAsync(expoUpdateConfigStr);
    showToast({ title: '热更新信息已复制' });
  }, [expoUpdateConfigStr]);

  const handleChangeNetworkLogEnabled = useCallback(
    async (checked: boolean) => {
      await setStorageEnableNetworkLogOnLaunch(String(checked));
      setIsStartingNetworkLoggingOnLaunchEnabled(checked);
    },
    [setStorageEnableNetworkLogOnLaunch],
  );

  const handleChangeMemoryLoggerEnabled = useCallback(
    async (checked: boolean) => {
      MemoryLogger.enable(checked);
      await setStorageEnableMemoryLogOnLaunch(String(checked));
      setIsStartingMemoryLoggerOnLaunchEnabled(checked);
    },
    [setStorageEnableMemoryLogOnLaunch],
  );

  const handlePressViewExpoUpdateConfig = useCallback(async () => {
    try {
      if (expoUpdateConfig) {
        const content = JSON.stringify(expoUpdateConfig, undefined, 2);
        router.push('/debugInfo', {
          title: 'ExpoUpdateConfig',
          info: content,
        });
      } else {
        showToast({ title: 'expoUpdate 配置为空' });
      }
    } catch (error) {
      showToast({ title: '读取 expoUpdate 配置失败' });
    }
  }, [expoUpdateConfig]);

  const handleJSBundleCheckUpdate = useCallback(async () => {
    toast.loading('检查热更新包中。。。');
    await manualCheckForUpdate(updateConfig, {
      onChecked: () => {
        toast.hide();
      },
      onNativeUpdateNeeded: () => {
        toast.hide();
        Alert.alert('热更新检查结果', '有新的原生包，请重新下载', [
          {
            text: '我知道了',
          },
        ]);
      },
      onJSUpdateAvailable: () => {
        toast.hide();
        toast.loading('有新的 JS Bundle，开始获取中。。。');
      },
      onJSUpdateDownloaded: (isNew) => {
        if (isNew) {
          Alert.alert('热更新检查结果', '下载热更新包成功，杀掉app重启生效', [
            {
              text: '立即重启',
              onPress: async () => await Updates.reloadAsync(),
            },
            {
              text: '稍后重启',
            },
          ]);
        } else {
          toast.error('获取热更新包失败');
        }
      },
      onNoUpdateAvailable: () => {
        toast.done('已经是 最新的 JS Bundle 了');
      },
      onError: (error) => {
        Alert.alert(`检查更新出错: ${error.message}`);
      },
    });
  }, [manualCheckForUpdate, toast, updateConfig]);

  const handlePressHotfixTest = useCallback(async () => {
    // const result = await native.hotfixTest();
    // showToast({ title: `热更新测试结果: ${result}` });

    router.push('/hotFixTest');
  }, []);

  const renderExpoUpdateConfigIfNeeded = useMemo(() => {
    if (__DEV__) {
      return false;
    }
    if (Updates.manifest) {
      const isUsingDeveloperTool = !!(Updates.manifest as any).developer?.tool;
      return !isUsingDeveloperTool;
    }
    return true;
  }, []);

  const handlePressDialogDemo = useCallback(() => {
    router.push('/debugAlert', {
      title: 'Dialog Demo',
    });
  }, []);

  return {
    appBuildInfo,
    appNativeBuildInfo,
    userInfoString,
    handlePressClearAsyncStorage,
    handlePressViewAsyncStorage,
    handlePressViewRedux,
    handlePressViewNet,
    isStartingNetworkLoggingOnLaunchEnabled,
    isNetworkLoggingEnabled,
    handleChangeNetworkLogEnabled,
    handlePressTamagui,
    handlePressUserInfo,
    handlePressViewExpoUpdateConfig,
    handleJSBundleCheckUpdate,
    updateReleaseChannel,
    handlePressHotfixTest,
    expoUpdateConfig,
    expoUpdateConfigStr,
    handlePressExpoUpdateConfig,
    handlePressSkiaDemo,
    handlePressRNViewsDemo,
    handlePressSectionListDemo,
    renderExpoUpdateConfigIfNeeded,
    updateInfoOnLaunch,
    handlePressJglUIV4,
    handlePressCrossPlatformComponents,
    handlePressPrivateJglUI,
    isMemoryLoggerEnabled,
    isStartingMemoryLoggerOnLaunchEnabled,
    handlePressViewMemoryLogger,
    handleChangeMemoryLoggerEnabled,
    handlePressDialogDemo,
  };
};

/**
 * 调试选项
 */
export default function DebugSettingsScreen() {
  const {
    handlePressClearAsyncStorage,
    handlePressViewAsyncStorage,
    handlePressViewRedux,
    handlePressViewNet,
    handlePressUserInfo,
    handleChangeNetworkLogEnabled,
    isNetworkLoggingEnabled,
    isStartingNetworkLoggingOnLaunchEnabled,
    handlePressTamagui,
    handlePressHotfixTest,
    userInfoString,
    appBuildInfo,
    appNativeBuildInfo,
    handlePressViewExpoUpdateConfig,
    handleJSBundleCheckUpdate,
    updateReleaseChannel,
    expoUpdateConfig,
    expoUpdateConfigStr,
    handlePressExpoUpdateConfig,
    renderExpoUpdateConfigIfNeeded,
    updateInfoOnLaunch,
    handlePressJglUIV4,
    handlePressCrossPlatformComponents,
    handlePressSkiaDemo,
    handlePressRNViewsDemo,
    handlePressPrivateJglUI,
    handlePressSectionListDemo,
    isMemoryLoggerEnabled,
    isStartingMemoryLoggerOnLaunchEnabled,
    handlePressViewMemoryLogger,
    handleChangeMemoryLoggerEnabled,
    handlePressDialogDemo,
  } = useDebugSettingsScreen();
  const { handlePressSwitchEnv } = useSwitchEnv();
  const { handlePressSwitchChannel } = useDebugSelectChannel();
  const { handlePressSwitchUpdateReleaseChannel } =
    useSwitchUpdateReleaseChannel();
  const currentEnv = envNames.find((i) => i.env === container.env().env())
    ?.name;

  const currentChannel = useAtomValue(currentChannelAtom);

  return (
    <>
      <Stack.Screen options={{ headerTitle: '调试菜单 - 测试' }} />
      <StatusBar style='dark' />
      <ScrollView
        className='flex-1 bg-white px-2'
        contentInset={{ bottom: 40, top: 20 }}
      >
        <YStack space='$3'>
          <Button onPress={handlePressSwitchChannel}>{`模拟渠道 - ${
            ALL_CHANNELS.find((i) => i.name === currentChannel)?.desc ?? '其他'
          }`}</Button>
          <Button
            onPress={handlePressSwitchEnv}
          >{`切换环境 - 当前${currentEnv}`}</Button>

          <Button onPress={handlePressSwitchUpdateReleaseChannel}>
            {`切换热更新渠道 - 当前${updateReleaseChannel}`}
          </Button>

          <XStack space='$2'>
            <Button className='flex-1' onPress={handlePressViewAsyncStorage}>
              查看Async Storage
            </Button>
            <Button className='flex-1' onPress={handlePressClearAsyncStorage}>
              清空Async Storage
            </Button>
          </XStack>

          <Button onPress={handlePressViewRedux}>查看Redux</Button>

          <Button onPress={handlePressViewExpoUpdateConfig}>
            立刻查看ExpoUpdateConfig
          </Button>

          <Button onPress={handleJSBundleCheckUpdate}>
            检查 JS Bundle 更新
          </Button>

          <YStack space='$1'>
            <Button onPress={handlePressViewNet}>
              查看网络请求（{isNetworkLoggingEnabled ? '已开启' : '未开启'}）
            </Button>
            <XStack className='items-center' space='$2'>
              <Text className='flex-1 text-right'>
                启动时打开网络请求log：
                {String(isStartingNetworkLoggingOnLaunchEnabled ?? false)}
              </Text>
              <Switch
                size='$4'
                checked={isStartingNetworkLoggingOnLaunchEnabled}
                onCheckedChange={handleChangeNetworkLogEnabled}
              >
                <Switch.Thumb />
              </Switch>
            </XStack>
          </YStack>

          <YStack space='$1'>
            <Button onPress={handlePressViewMemoryLogger}>
              查看内存日志（{isMemoryLoggerEnabled ? '已开启' : '未开启'}）
            </Button>
            <XStack className='items-center' space='$2'>
              <Text className='flex-1 text-right'>
                启动时打开内存日志：
                {String(isStartingMemoryLoggerOnLaunchEnabled ?? false)}
              </Text>
              <Switch
                size='$4'
                checked={isStartingMemoryLoggerOnLaunchEnabled}
                onCheckedChange={handleChangeMemoryLoggerEnabled}
              >
                <Switch.Thumb />
              </Switch>
            </XStack>
          </YStack>

          {/* <Button onPress={handlePressTamagui}>Tamagui</Button> */}
          <Button onPress={handlePressCrossPlatformComponents}>
            UI - cross-platform-components
          </Button>
          <Button onPress={handlePressSkiaDemo}>UI - Skia Demo</Button>
          <Button onPress={handlePressSectionListDemo}>
            UI - SectionList Demo
          </Button>
          <Button onPress={handlePressRNViewsDemo}>UI - RNViews Demo</Button>
          <Button onPress={handlePressJglUIV4}>UI - @jgl/ui-v4</Button>

          <Button onPress={handlePressHotfixTest}>
            热更新测试 JS 调用不存在的原生代码
          </Button>
          <Button onPress={handlePressDialogDemo}>弹窗组件 demo</Button>

          {appNativeBuildInfo ? (
            <YStack space='$2'>
              {appNativeBuildInfo.appNativeBuildTime ? (
                <XStack space='$2'>
                  <Text>App 原生包打包时间</Text>
                  <Text>{appNativeBuildInfo.appNativeBuildTime}</Text>
                </XStack>
              ) : null}

              {appNativeBuildInfo.appNativeBuildBranch ? (
                <XStack space='$2'>
                  <Text>App 原生包打包分支</Text>
                  <Text>{appNativeBuildInfo.appNativeBuildBranch}</Text>
                </XStack>
              ) : null}

              {appNativeBuildInfo.appNativeBuildCommit ? (
                <XStack space='$2'>
                  <Text>App 原生包打包 commit</Text>
                  <Text>{appNativeBuildInfo.appNativeBuildCommit}</Text>
                </XStack>
              ) : null}

              {renderExpoUpdateConfigIfNeeded &&
              expoUpdateConfig?.runtimeVersion ? (
                <YStack space='$2'>
                  <XStack space='$2'>
                    <Text>热更新版本号</Text>
                    <Text>{expoUpdateConfig.runtimeVersion}</Text>
                  </XStack>
                </YStack>
              ) : null}
            </YStack>
          ) : null}

          {appBuildInfo ? (
            <YStack space='$2'>
              {appBuildInfo.appBuildTime ? (
                <XStack space='$2'>
                  <Text>JS Bundle 打包时间</Text>
                  <Text>{appBuildInfo.appBuildTime}</Text>
                </XStack>
              ) : null}

              {appBuildInfo.appBuildBranch ? (
                <XStack space='$2'>
                  <Text>JS Bundle 打包分支</Text>
                  <Text>{appBuildInfo.appBuildBranch}</Text>
                </XStack>
              ) : null}

              {appBuildInfo.appBuildCommit ? (
                <XStack space='$2'>
                  <Text>JS Bundle 打包 commit</Text>
                  <Text>{appBuildInfo.appBuildCommit}</Text>
                </XStack>
              ) : null}

              {renderExpoUpdateConfigIfNeeded &&
              expoUpdateConfig?.runtimeVersion ? (
                <YStack space='$2'>
                  <XStack space='$2'>
                    <Text>热更新版本号(原生包版本号+原生包构建时间)</Text>
                    <Text>{expoUpdateConfig.runtimeVersion}</Text>
                  </XStack>
                  <XStack space='$2'>
                    <Text>原生包版本号</Text>
                    <Text>
                      {expoUpdateConfig.runtimeVersion.substring(
                        0,
                        expoUpdateConfig.runtimeVersion.lastIndexOf('.'),
                      )}
                    </Text>
                  </XStack>
                  <XStack space='$2'>
                    <Text>原生包构建时间</Text>
                    <Text>
                      {expoUpdateConfig.runtimeVersion.substring(
                        expoUpdateConfig.runtimeVersion.lastIndexOf('.') + 1,
                      )}
                    </Text>
                  </XStack>
                </YStack>
              ) : null}
            </YStack>
          ) : null}

          <Text>用户信息</Text>
          <Text onPress={handlePressUserInfo}>{userInfoString}</Text>

          {renderExpoUpdateConfigIfNeeded && expoUpdateConfigStr ? (
            <>
              <Text>热更新信息</Text>
              <Text onPress={handlePressExpoUpdateConfig}>
                {expoUpdateConfigStr}
              </Text>
            </>
          ) : null}

          {renderExpoUpdateConfigIfNeeded && updateInfoOnLaunch ? (
            <>
              <Text>本次启动热更新信息</Text>
              <YStack space='$2'>
                <Text>updateId: {updateInfoOnLaunch.updateId}</Text>
                <Text>runtimeVersion: {updateInfoOnLaunch.runtimeVersion}</Text>
                <Text>
                  checkAutomatically: {updateInfoOnLaunch.checkAutomatically}
                </Text>
                <Text>
                  isEmergencyLaunch: {updateInfoOnLaunch.isEmergencyLaunch}
                </Text>
                <Text>
                  isEmbeddedLaunch: {updateInfoOnLaunch.isEmbeddedLaunch}
                </Text>
              </YStack>
            </>
          ) : null}
        </YStack>
      </ScrollView>
    </>
  );
}

const useDebugSelectChannel = () => {
  const { showActionSheetWithOptions } = useActionSheet();
  const setCurrentChannel = useSetAtom(currentChannelAtom);

  const handlePressSwitchChannel = useCallback(() => {
    showActionSheetWithOptions(
      {
        options: ALL_CHANNELS.map((i) => `${i.name}（${i.desc}）`),
        title: '模拟渠道',
        containerStyle: {
          maxHeight: '50%',
        },
        cancelButtonIndex: 0,
      },
      (selectedIndex) => {
        if (selectedIndex != null) {
          const selectedChannel = ALL_CHANNELS[selectedIndex];
          if (selectedChannel) {
            setCurrentChannel(selectedChannel.name);
          }
        }
      },
    );
  }, [setCurrentChannel, showActionSheetWithOptions]);

  return { handlePressSwitchChannel };
};

const useSwitchEnv = () => {
  const { showActionSheetWithOptions } = useActionSheet();

  const { logInUuid } = useLogIn();

  const { logout } = useLogout();

  const userId = useAppSelector((state) => state.userInfo?.userId);

  const handlePressSwitchEnv = useCallback(() => {
    const textCancel = '取消';
    const options = [...envNames.map((i) => i.name), textCancel];
    const indexCancel = options.indexOf(textCancel);

    showActionSheetWithOptions(
      {
        options,
        cancelButtonIndex: indexCancel,
      },
      async (selectedIndex) => {
        switch (selectedIndex) {
          case indexCancel: {
            break;
          }
          default: {
            if (selectedIndex != null) {
              const envName = options[selectedIndex];
              if (envName) {
                const selectedEnv = envNames.find((i) => i.name === envName)
                  ?.env;
                if (selectedEnv) {
                  if (userId) {
                    logout();
                    container.env().updateEnv(selectedEnv);
                    Alert.alert(`已切换到${selectedEnv}，杀掉app重启生效`);
                  } else {
                    container.env().updateEnv(selectedEnv);
                    Alert.alert(`已切换到${selectedEnv}，杀掉app重启生效`);
                  }
                }
              }
            }
          }
        }
      },
    );
  }, [logout, showActionSheetWithOptions, userId]);

  return { handlePressSwitchEnv };
};

const useSwitchUpdateReleaseChannel = () => {
  const { showActionSheetWithOptions } = useActionSheet();
  const { switchReleaseChannel } = useExpoUpdate();
  const handlePressSwitchUpdateReleaseChannel = useCallback(() => {
    const textCancel = '取消';
    const options = [
      ...updateReleaseChannelNames.map((i) => i.name),
      textCancel,
    ];
    const indexCancel = options.indexOf(textCancel);
    showActionSheetWithOptions(
      {
        options,
        cancelButtonIndex: indexCancel,
      },
      async (selectedIndex) => {
        switch (selectedIndex) {
          case indexCancel: {
            break;
          }
          default: {
            if (selectedIndex != null) {
              const channelName = options[selectedIndex];
              if (channelName) {
                const selectedChannel = updateReleaseChannelNames.find(
                  (i) => i.name === channelName,
                )?.channel;

                if (selectedChannel) {
                  await switchReleaseChannel(selectedChannel);
                  Alert.alert(
                    `已切换热更新渠道到${selectedChannel}，杀掉app重启生效`,
                  );
                }
              }
            }
          }
        }
      },
    );
  }, [showActionSheetWithOptions, switchReleaseChannel]);

  return { handlePressSwitchUpdateReleaseChannel };
};
