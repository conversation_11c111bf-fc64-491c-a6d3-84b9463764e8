import {
  YTAlert,
  YTAlertActionType,
  YTYStack,
  type YTAlertRef,
} from '@bookln/cross-platform-components';
import { useFloatingAudioBallContext } from '@jgl/biz-components';
import { Fragment, useCallback, useRef } from 'react';
import { Button } from 'tamagui';

/**
 * @description 弹窗组件 demo
 * <AUTHOR>
 * @date 2025-06-26
 */
const DebugAlertScreen = () => {
  const alertRef = useRef<YTAlertRef>(null);

  const { show, hide, setAudioInfo } = useFloatingAudioBallContext() ?? {};

  const onPressRefOpen = useCallback(() => {
    alertRef.current?.show({
      title: '主标题展示位置',
      message:
        '正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域',
      actions: [
        {
          label: '删除',
          type: YTAlertActionType.危险按扭,
          onPress: (hide) => {
            hide();
          },
        },
      ],
    });
  }, []);

  // ✨ 静态方法调用示例
  const onPressStaticShow = useCallback(() => {
    YTAlert.show({
      title: '静态方法调用',
      message: '这是通过静态方法调用的弹窗，类似于 antd 的 Modal.confirm()',
      onOk: (close) => {
        console.log('静态方法 - 确定');
        close();
      },
      onCancel: (close) => {
        console.log('静态方法 - 取消');
        close();
      },
    });
  }, []);

  const onPressStaticCustom = useCallback(() => {
    YTAlert.show({
      title: '自定义按钮',
      message: '静态方法支持自定义按钮组',
      actions: [
        {
          label: '删除',
          type: YTAlertActionType.危险按扭,
          onPress: (close) => {
            console.log('删除操作');
            close();
          },
        },
        {
          label: '编辑',
          type: YTAlertActionType.主要按扭,
          onPress: (close) => {
            console.log('编辑操作');
            close();
          },
        },
        {
          label: '取消',
          type: YTAlertActionType.默认,
          onPress: (close) => {
            console.log('取消操作');
            close();
          },
        },
      ],
    });
  }, []);

  const onPressShowBall = useCallback(() => {
    show?.({ title: 'Lover - Taylor Swift' });
  }, [show]);

  return (
    <Fragment>
      <YTYStack flex={1} gap={8} p={12}>
        <YTAlert
          trigger={(onPress) => {
            return <Button onPress={onPress}>Trigger</Button>;
          }}
          title='主标题展示位置主标题展示位置主标题展示位置主标题展示位置'
          message='正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域正文描述展示区域'
          onOk={async (hide) => {
            return new Promise((resolve) => {
              setTimeout(() => {
                resolve();
                hide();
              }, 2000);
            });
          }}
          onCancel={(hide) => {
            hide();
          }}
          actions={[
            {
              label: '删除',
              type: YTAlertActionType.危险按扭,
              onPress: (hide) => {
                hide();
              },
            },
          ]}
        />

        <Button onPress={onPressRefOpen}>Ref 调用</Button>

        {/* ✨ 静态方法调用示例 */}
        <Button onPress={onPressStaticShow}>静态方法调用</Button>

        <Button onPress={onPressStaticCustom}>静态方法 - 自定义按钮</Button>

        <Button onPress={onPressShowBall}>Show Ball</Button>
      </YTYStack>

      <YTAlert ref={alertRef} />
    </Fragment>
  );
};

export default DebugAlertScreen;
