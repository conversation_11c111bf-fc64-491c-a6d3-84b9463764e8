import { BooklnLoginComponents } from '@bookln/bookln-biz';
import { StatusBar } from 'expo-status-bar';
import { useDismissKeyboardOnDisappear } from '../../hooks/useDismissKeyboardOnDisappear';
import { Stack } from 'expo-router';

const { BindMobileContent } = BooklnLoginComponents;

export default function BindPhoneScreen() {
  useDismissKeyboardOnDisappear();

  return (
    <>
      <Stack.Screen options={{ title: '绑定手机号' }} />
      <StatusBar style='dark' />
      <BindMobileContent />
    </>
  );
}
