import { BooklnLoginComponents } from '@bookln/bookln-biz';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';

const { BindPhonePrepareContent } = BooklnLoginComponents;

export default function BindPhonePrepareScreen() {
  return (
    <>
      <Stack.Screen options={{ title: '绑定手机号' }} />
      <StatusBar style='dark' />
      <BindPhonePrepareContent />
    </>
  );
}
