import { useOrientation } from '@jgl/utils';
import type { DrawerContentComponentProps } from '@react-navigation/drawer';
import { Drawer } from 'expo-router/drawer';
import { useAtomValue } from 'jotai';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { atomMap } from '../../atom';
import { ChatSessionDrawerContent } from '../../components/ChatSessionDrawerContent';

export default function TabLayout() {
  const [drawerKey, setDrawerKey] = useState(1);

  const homeTabIndex = useAtomValue(atomMap.homeTabIndexAtom);

  const isHomeSelected = useMemo(() => homeTabIndex === 0, [homeTabIndex]);

  const orientation = useOrientation();

  const renderDrawerContent = useCallback(
    (props: DrawerContentComponentProps) => {
      return <ChatSessionDrawerContent />;
    },
    [],
  );

  useEffect(() => {
    /** 屏幕旋转的时候，重新渲染 Drawer */
    if (orientation) {
      setDrawerKey((prevKey) => prevKey + 1);
    }
  }, [orientation]);

  return (
    <Drawer
      key={drawerKey}
      screenOptions={{
        drawerType: 'slide',
        drawerPosition: 'left',
        drawerStyle: {
          width: '85%',
        },
        // overlayColor: 'rgba(0, 0, 0, 0.4)',
        swipeEnabled: isHomeSelected,
        headerShown: false,
        // swipeEdgeWidth: windowWidth,
      }}
      defaultStatus='closed'
      drawerContent={renderDrawerContent}
    >
      <Drawer.Screen
        name='home'
        options={{
          title: '首页',
        }}
      />
    </Drawer>
  );
}
