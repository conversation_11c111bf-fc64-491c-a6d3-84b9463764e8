import ImageResizer from '@bam.tech/react-native-image-resizer';
import {
  YTImage,
  YTText,
  YTTouchable,
  YTXStack,
  YTYStack,
} from '@bookln/cross-platform-components';
import {
  PermissionEnum,
  PermissionHooks,
  PermissionPurposeScene,
} from '@bookln/permission';
import {
  useNavigationBarBarHeight,
  useNavigationBarHeight,
  useSafeAreaInsets,
} from '@jgl/biz-func';
import { container } from '@jgl/container';
import type {
  CameraProps,
  CameraRuntimeError,
} from 'react-native-vision-camera';
import {
  Camera,
  useCameraDevice,
  useCameraFormat,
} from 'react-native-vision-camera';
import { OSSUploadNative } from '@jgl/upload/OSSUpload';
import { showToast, useDidHide, useDidShow } from '@jgl/utils';
import {
  Canvas,
  Image as SkiaImage,
  useImage,
} from '@shopify/react-native-skia';
import { useUnmount } from 'ahooks';
import type { CameraView } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import { router, Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import type { GestureResponderEvent } from 'react-native';
import {
  type LayoutChangeEvent,
  Platform,
  StyleSheet,
  useWindowDimensions,
  View,
} from 'react-native';
import RNCanvas from 'react-native-canvas';
import Reanimated, {
  Extrapolate,
  interpolate,
  useAnimatedGestureHandler,
  useAnimatedProps,
  useAnimatedReaction,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';
import { addTextTransTas, checkTextTrans } from '../../api/TranslateServiceApi';
import { ColorfulActivityIndicator } from '../../components/ColorfulActivityIndicator';
import { useIsFocused } from '@react-navigation/native';
import { useIsForeground } from '../../hooks/useIsForeground';
import { usePreferredCameraDevice } from '../../hooks/usePreferredCameraDevice';
import type { PinchGestureHandlerGestureEvent } from 'react-native-gesture-handler';
import {
  PinchGestureHandler,
  TapGestureHandler,
} from 'react-native-gesture-handler';

const SCALE_FULL_ZOOM = 3;
const MAX_ZOOM_FACTOR = 10;
const ReanimatedCamera = Reanimated.createAnimatedComponent(Camera);
Reanimated.addWhitelistedNativeProps({
  zoom: true,
});

// 轮询间隔时间
const POLL_INTERVAL = 400;
// 轮询超时时间
const POLL_TIMEOUT = 10 * 1000;

// 扫描动画组件
function ScanEffectAnimation({
  width,
  height,
  visible,
}: {
  width: number;
  height: number;
  visible: boolean;
}) {
  const scanImg = useImage(require('../../assets/images/ic_scan_effect.png'));
  const translateY = useSharedValue(0);

  // 图片实际高度
  const imgHeight = 128;

  // 动画启动
  useAnimatedReaction(
    () => visible && !!scanImg,
    (v, prev) => {
      if (v && !prev) {
        translateY.value = 0;
        translateY.value = withRepeat(
          withTiming(Math.max(0, height - imgHeight), { duration: 1200 }),
          -1,
          false,
        );
      }
      if ((!v && prev) || !scanImg) {
        translateY.value = 0;
      }
    },
    [visible, height, imgHeight, scanImg],
  );

  if (!visible || !scanImg) return null;

  return (
    <Canvas
      style={{
        position: 'absolute',
        left: 0,
        top: 0,
        width,
        height,
        pointerEvents: 'none',
        zIndex: 10,
      }}
    >
      <SkiaImage
        image={scanImg}
        x={0}
        y={translateY}
        width={width}
        height={imgHeight}
        fit='cover'
      />
    </Canvas>
  );
}

export default function TakePhotoTranslationCamera() {
  const safeInsets = useSafeAreaInsets();
  const navigationBarHeight = useNavigationBarHeight();
  const barHeight = useNavigationBarBarHeight();
  const windowSize = useWindowDimensions();

  const camera = useRef<Camera>(null);
  const [isCameraInitialized, setIsCameraInitialized] = useState(false);
  const zoom = useSharedValue(1);
  // check if camera page is active
  const isFocussed = useIsFocused();
  const isForeground = useIsForeground();
  const isActive = isFocussed && isForeground;
  const [preferredDevice] = usePreferredCameraDevice();
  let device = useCameraDevice('back');

  if (preferredDevice != null && preferredDevice.position === 'back') {
    // override default device with the one selected by the user in settings
    device = preferredDevice;
  }

  const minZoom = device?.minZoom ?? 1;
  const maxZoom = Math.min(device?.maxZoom ?? 1, MAX_ZOOM_FACTOR);

  const cameraAnimatedProps = useAnimatedProps<CameraProps>(() => {
    const z = Math.max(Math.min(zoom.value, maxZoom), minZoom);
    return {
      zoom: z,
    };
  }, [maxZoom, minZoom, zoom]);
  const [cameraLayout, setCameraLayout] = useState<{
    width: number;
    height: number;
  }>({ width: 0, height: 0 });
  const screenAspectRatio = cameraLayout.height / cameraLayout.width;
  const targetFps = 60;
  const format = useCameraFormat(device, [
    { fps: targetFps },
    { videoAspectRatio: screenAspectRatio },
    { videoResolution: 'max' },
    { photoAspectRatio: screenAspectRatio },
    { photoResolution: 'max' },
  ]);

  const fps = Math.min(format?.maxFps ?? 1, targetFps);

  const supportsFlash = device?.hasFlash ?? false;
  const supportsHdr = format?.supportsPhotoHdr;
  const supports60Fps = useMemo(
    () => device?.formats.some((f) => f.maxFps >= 60),
    [device?.formats],
  );

  useEffect(() => {
    // Reset zoom to it's default everytime the `device` changes.
    zoom.value = device?.neutralZoom ?? 1;
  }, [zoom, device]);

  useEffect(() => {
    const f =
      format != null
        ? `(${format.photoWidth}x${format.photoHeight} photo / ${format.videoWidth}x${format.videoHeight}@${format.maxFps} video @ ${fps}fps)`
        : undefined;
    console.log(`Camera: ${device?.name} | Format: ${f}`);
  }, [device?.name, format, fps]);

  const cameraRef = useRef<CameraView>(null);
  const [enableTorch, setEnableTorch] = useState<boolean>(false);
  const { checkAndRequestPermission } = PermissionHooks.usePermission();
  const [isUploading, setIsUploading] = useState(false);
  const [selectedPhotoUri, setSelectedPhotoUri] = useState<string | null>(null);

  const [canvasSize, setCanvasSize] = useState<{
    width: number;
    height: number;
  }>();

  const onLayout = useCallback((event: LayoutChangeEvent) => {
    const { width, height } = event.nativeEvent.layout;
    setCanvasSize({ width, height });
  }, []);

  const [processing, setProcessing] = useState(false);

  const isMountRef = useRef(true);
  const [cameraActive, setCameraActive] = useState(true);
  console.log('🚀 ~ TakePhotoTranslationCamera ~ cameraActive:', cameraActive);

  useDidShow(() => {
    console.log('🚀 ~ TakePhotoTranslationCamera ~ useDidShow');
    setCameraActive(true);
  });

  useDidHide(() => {
    console.log('🚀 ~ TakePhotoTranslationCamera ~ useDidHide');
    setCameraActive(false);
  });

  const checkTextTransTaskResult = useCallback(
    async (recordId: string) => {
      const request = checkTextTrans({ recordId });
      const response = await container.net().fetch(request);
      if (response.success && response.data) {
        console.log('🚀 ~ checkTextTransTaskResult ~ response:', response.data);
        if (
          response.data.alignUrl &&
          selectedPhotoUri !== response.data.alignUrl
        ) {
          setSelectedPhotoUri(response.data.alignUrl);
        }
        if (response.data.dataList.length > 0) {
          return response.data;
        } else {
          return null;
        }
      } else {
        return null;
      }
    },
    [selectedPhotoUri],
  );

  const startPollingTextTranslationTaskResult = useCallback(
    async (recordId: string) => {
      let timeout: ReturnType<typeof setTimeout>;
      let interval: ReturnType<typeof setInterval>;
      interval = setInterval(async () => {
        if (!isMountRef.current) {
          clearInterval(interval);
          clearTimeout(timeout);
          return;
        }
        console.log(
          'leejunhui - 🔥🔥🔥🔥🔥🔥 - startPollingTextTranslationTaskResult',
          recordId,
        );
        const result = await checkTextTransTaskResult(recordId);
        if (result) {
          router.push({
            pathname: '/(takePhotoTranslation)/takePhotoTranslationResult',
            params: {
              recordId: result.recordId,
              imageUrl: result.alignUrl,
              data: JSON.stringify(result.dataList),
            },
          });
          clearInterval(interval);
          clearTimeout(timeout);
          setTimeout(() => {
            setProcessing(false);
            setSelectedPhotoUri(null);
          }, 1000);
        }
      }, POLL_INTERVAL);
      timeout = setTimeout(() => {
        clearInterval(interval);
        setProcessing(false);
        setSelectedPhotoUri(null);
        showToast({
          title: '处理超时',
        });
      }, POLL_TIMEOUT);
    },
    [checkTextTransTaskResult],
  );

  const submitTextTranslationTask = useCallback(
    async (param: { imageUrl: string }) => {
      const { imageUrl } = param;
      const request = addTextTransTas({
        imageUrl,
      });
      const response = await container.net().fetch(request);
      if (response.success && response.data) {
        const recordId = response.data;
        console.log('TakePhotoTranslationCamera - response', response.data);
        // 开启轮询
        startPollingTextTranslationTaskResult(recordId);

        // TODO: chenfeng - 2025-07-15 接口现在有问题，暂时直接使用本地数据
        // router.push({
        //   pathname: '/(takePhotoTranslation)/takePhotoTranslationResult',
        //   params: {
        //     recordId: MockPhotoTranslationResult.recordId,
        //     imageUrl: MockPhotoTranslationResult.alignUrl,
        //     data: MockPhotoTranslationResult.data,
        //   },
        // });
      } else {
        setProcessing(false);
        showToast({
          title: '处理失败',
        });
        // TODO: chenfeng - 2025-07-15 埋点
      }
    },
    [startPollingTextTranslationTaskResult],
  );

  const onCommitPhoto = useCallback(
    async (param: {
      url: string;
      /**
       * 图片大小 in Bytes
       */
      size: number;
      width: number;
      height: number;
    }) => {
      const { url, width, height } = param;
      setProcessing(true);
      setIsUploading(true);
      setSelectedPhotoUri(url);

      // 上传前对图片进行压缩
      const maxWidth = 1920;
      const maxHeight = 1920;
      try {
        const compressedResponse = await ImageResizer.createResizedImage(
          url,
          maxWidth,
          maxHeight,
          'JPEG',
          50,
        );
        console.log(
          '🚀 ~ TakePhotoTranslationCamera ~ compressedResponse:',
          compressedResponse,
        );
        if (compressedResponse.uri) {
          const imageUrl = await OSSUploadNative.uploadToOSSWith90DaysExpiry({
            tmpPath: compressedResponse.uri,
            options: {
              net: container.net(),
            },
            bizCode: 'live',
            imageDimensions: { width, height },
          });
          setIsUploading(false);
          if (imageUrl) {
            submitTextTranslationTask({ imageUrl });
            console.log('TakePhotoTranslationCamera - url', url, imageUrl);
          } else {
            setProcessing(false);
            showToast({
              title: '上传失败',
            });
          }
        } else {
          setProcessing(false);
          showToast({
            title: '图片压缩失败',
          });
        }
      } catch (error) {
        console.log(
          'TakePhotoTranslationCamera - onCommitPhoto - error',
          error,
        );
        setProcessing(false);
        showToast({
          title: '图片上传失败',
        });
      }
    },
    [submitTextTranslationTask],
  );

  const onPressTakePhoto = useCallback(async () => {
    console.log('onPressTakePhoto --------');
    if (camera) {
      if (isUploading) {
        return;
      }

      try {
        const photo = await camera.current?.takePhoto({
          flash: enableTorch ? 'on' : 'off',
          enableShutterSound: false,
        });
        // const result = await cameraRef.current.takePictureAsync({
        //   base64: false,
        //   quality: 1,
        // });
        // console.log('take Photo result', JSON.stringify(result));

        if (photo?.path) {
          onCommitPhoto({
            url: photo.path,
            width: photo.width,
            height: photo.height,
            size: 0,
          });
        }
      } catch (error) {
        console.log('take Photo error', JSON.stringify(error));
      }
    }
  }, [enableTorch, isUploading, onCommitPhoto]);

  const onPressNavBack = useCallback(() => {
    router.back();
  }, []);

  useUnmount(() => {
    isMountRef.current = false;
  });

  const renderNavBar = useMemo(() => {
    return (
      <YTXStack
        position='absolute'
        top={0}
        left={0}
        right={0}
        zIndex={1000}
        w='$full'
        h={safeInsets.top + navigationBarHeight}
        ai='center'
        bg='transparent'
      >
        <YTXStack
          position='absolute'
          w='$full'
          left={0}
          right={0}
          top={safeInsets.top}
          h={Platform.OS === 'ios' ? barHeight : undefined}
          py={10}
          px={16}
          jc='space-between'
          ai='center'
          bg='transparent'
        >
          <YTTouchable
            ai='center'
            jc='center'
            onPress={onPressNavBack}
            style={{
              width: 32,
              height: 32,
            }}
          >
            <YTImage
              source={require('../../assets/images/icon_back_white_bg_black_line.png')}
              w={32}
              h={32}
            />
          </YTTouchable>
        </YTXStack>
      </YTXStack>
    );
  }, [barHeight, navigationBarHeight, onPressNavBack, safeInsets.top]);

  const onPressOpenAlbum = useCallback(async () => {
    console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - onPressOpenAlbum');
    if (isUploading) {
      return;
    }

    const authResult = await checkAndRequestPermission({
      permission: PermissionEnum.Album,
      scene: PermissionPurposeScene.ChoicePicture,
    });
    if (authResult) {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        aspect: [4, 3],
        quality: 1,
        selectionLimit: 1,
      });
      console.log('🚀 ~ handlePickPhoto ~ result:', result);
      if (!result.canceled) {
        if (result.assets.length > 0) {
          const asset = result.assets[0];
          if (asset) {
            const { uri, width, height } = asset;
            onCommitPhoto({
              url: uri,
              width,
              height,
              size: 0,
            });
          }
        }
      }
    }
  }, [checkAndRequestPermission, isUploading, onCommitPhoto]);

  const renderBottomPanel = useMemo(() => {
    if (processing) {
      return (
        <YTYStack
          position='fixed'
          w='$full'
          left={0}
          right={0}
          bottom={0}
          px={24}
          pb={safeInsets.bottom}
          h={142 + safeInsets.bottom}
          ai='center'
          justifyContent='center'
          gap={10}
          bg='transparent'
        >
          <ColorfulActivityIndicator size={30} />
          <YTText color='#FCFCFC' fontSize={14} lineHeight={22}>
            智能翻译中
          </YTText>
        </YTYStack>
      );
    }
    return (
      <YTXStack
        position='fixed'
        w='$full'
        left={0}
        right={0}
        bottom={0}
        px={24}
        pb={safeInsets.bottom}
        h={142 + safeInsets.bottom}
        ai='center'
        justifyContent='space-between'
        bg='transparent'
        zIndex={9999}
      >
        <YTTouchable
          w={44}
          h={44}
          bg='#262626'
          ai='center'
          jc='center'
          borderRadius={9999}
          onPress={onPressOpenAlbum}
        >
          <YTImage
            source={require('../../assets/images/ic_open_album.png')}
            w={24}
            h={24}
          />
        </YTTouchable>
        <YTTouchable
          w={64}
          h={64}
          bg='#262626'
          ai='center'
          jc='center'
          borderRadius={9999}
          onPress={onPressTakePhoto}
        >
          <YTImage
            source={require('../../assets/images/ic_take_photo_btn.png')}
            w={64}
            h={64}
          />
        </YTTouchable>
        <YTTouchable
          w={44}
          h={44}
          bg={enableTorch === false ? '#262626' : 'white'}
          ai='center'
          jc='center'
          borderRadius={9999}
          onPress={() => setEnableTorch((prev) => !prev)}
        >
          <YTImage
            source={
              enableTorch === false
                ? require('../../assets/images/ic_flash_light_off.png')
                : require('../../assets/images/ic_flash_light_on.png')
            }
            w={24}
            h={24}
          />
        </YTTouchable>
      </YTXStack>
    );
  }, [
    enableTorch,
    onPressOpenAlbum,
    onPressTakePhoto,
    processing,
    safeInsets.bottom,
  ]);

  const scanAnimationVisible = useMemo(() => {
    return (isUploading || processing) && !!selectedPhotoUri;
  }, [isUploading, processing, selectedPhotoUri]);

  /** 绘制参考线 */
  const handleCanvas = useCallback(
    (canvas: RNCanvas) => {
      if (!canvas || !canvasSize) return;
      const { width, height } = canvasSize;
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');
      ctx.strokeStyle = 'white';
      ctx.lineWidth = 0.5;
      ctx.beginPath();
      ctx.moveTo(width / 3, 0);
      ctx.lineTo(width / 3, height);
      ctx.stroke();
      ctx.moveTo((width / 3) * 2, 0);
      ctx.lineTo((width / 3) * 2, height);
      ctx.stroke();
      ctx.moveTo(0, height / 3);
      ctx.lineTo(width, height / 3);
      ctx.stroke();
      ctx.moveTo(0, (height / 3) * 2);
      ctx.lineTo(width, (height / 3) * 2);
      ctx.stroke();

      ctx.font = '16px sans-serif';
      ctx.fillStyle = 'white';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText('拍照翻译整页图片', width / 2, height / 2 - 12);
    },
    [canvasSize],
  );

  const onPinchGesture = useAnimatedGestureHandler<
    PinchGestureHandlerGestureEvent,
    { startZoom?: number }
  >({
    onStart: (_, context) => {
      context.startZoom = zoom.value;
    },
    onActive: (event, context) => {
      // we're trying to map the scale gesture to a linear zoom here
      const startZoom = context.startZoom ?? 0;
      const scale = interpolate(
        event.scale,
        [1 - 1 / SCALE_FULL_ZOOM, 1, SCALE_FULL_ZOOM],
        [-1, 0, 1],
        Extrapolate.CLAMP,
      );
      zoom.value = interpolate(
        scale,
        [-1, 0, 1],
        [minZoom, startZoom, maxZoom],
        Extrapolate.CLAMP,
      );
    },
  });

  const onFocusTap = useCallback(
    ({ nativeEvent: event }: GestureResponderEvent) => {
      if (!device?.supportsFocus) return;
      camera.current?.focus({
        x: event.locationX,
        y: event.locationY,
      });
    },
    [device?.supportsFocus],
  );

  const onInitialized = useCallback(() => {
    console.log('Camera initialized!');
    setIsCameraInitialized(true);
  }, []);

  const onError = useCallback((error: CameraRuntimeError) => {
    console.log('Camera onError!');
    console.error(error);
  }, []);

  const renderCanvas = useMemo(() => {
    if (canvasSize) {
      return (
        <View pointerEvents='none' style={StyleSheet.absoluteFill}>
          <RNCanvas ref={handleCanvas} />
        </View>
      );
    }
  }, [canvasSize, handleCanvas]);

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar style='light' />
      <YTYStack flex={1} bg='black' position='relative'>
        {renderNavBar}
        {selectedPhotoUri ? (
          <YTYStack
            flex={1}
            position='relative'
            onLayout={(e) => {
              const { width, height } = e.nativeEvent.layout;
              console.log(
                '🚀 ~ TakePhotoTranslationCamera ~ width: height:',
                width,
                height,
              );
              setCameraLayout({ width, height });
            }}
            bg='transparent'
          >
            <YTImage
              flex={1}
              resizeMode='contain'
              source={selectedPhotoUri}
              w='$full'
              h='$full'
            />
            {/* 扫描动画 */}
            <ScanEffectAnimation
              width={cameraLayout.width}
              height={cameraLayout.height}
              visible={scanAnimationVisible}
            />
          </YTYStack>
        ) : (
          <YTYStack
            flex={1}
            position='relative'
            onLayout={(e) => {
              const { width, height } = e.nativeEvent.layout;
              setCameraLayout({ width, height });
            }}
            // bg='red'
          >
            {device ? (
              <PinchGestureHandler
                onGestureEvent={onPinchGesture}
                enabled={isActive}
              >
                <Reanimated.View
                  onTouchEnd={onFocusTap}
                  style={StyleSheet.absoluteFill}
                >
                  <ReanimatedCamera
                    className='w-full flex-1'
                    style={{
                      width: '100%',
                      height: '100%',
                      backgroundColor: 'black',
                    }}
                    isActive={isActive}
                    device={device}
                    ref={camera}
                    onInitialized={onInitialized}
                    animatedProps={cameraAnimatedProps}
                    onError={onError}
                    format={format}
                    fps={fps}
                    photoQualityBalance={'quality'}
                    enableZoomGesture={false}
                    exposure={0}
                    photo={true}
                    torch={enableTorch ? 'on' : 'off'}
                    onLayout={!canvasSize ? onLayout : undefined}
                  />
                </Reanimated.View>
              </PinchGestureHandler>
            ) : (
              <YTYStack flex={1} jc='center' ai='center' bg='black'>
                <YTText color='white'>当前设备不支持相机</YTText>
              </YTYStack>
            )}
            {/* {Platform.OS === 'ios' && device ? (
              // <CameraView
              //   active={cameraActive}
              //   className='w-full flex-1'
              //   style={{
              //     width: '100%',
              //     height: '100%',
              //     backgroundColor: 'black',
              //   }}
              //   ref={cameraRef}
              //   facing='back'
              //   onLayout={!canvasSize ? onLayout : undefined}
              //   enableTorch={enableTorch}
              //   onMountError={(e) => {
              //     console.log(
              //       'leejunhui - 🔥🔥🔥🔥🔥🔥 - onMountError',
              //       e.message,
              //     );
              //   }}
              // />
              <PinchGestureHandler
                onGestureEvent={onPinchGesture}
                enabled={isActive}
              >
                <Reanimated.View
                  onTouchEnd={onFocusTap}
                  style={StyleSheet.absoluteFill}
                >
                  <ReanimatedCamera
                    className='w-full flex-1'
                    style={{
                      width: '100%',
                      height: '100%',
                      backgroundColor: 'black',
                    }}
                    isActive={isActive}
                    device={device}
                    ref={camera}
                    onInitialized={onInitialized}
                    animatedProps={cameraAnimatedProps}
                    onError={onError}
                    format={format}
                    fps={fps}
                    photoQualityBalance={'quality'}
                    enableZoomGesture={false}
                    exposure={0}
                    photo={true}
                    torch={enableTorch ? 'on' : 'off'}
                  />
                </Reanimated.View>
              </PinchGestureHandler>
            ) : cameraActive ? (
              <CameraView
                active={cameraActive}
                className='w-full flex-1'
                style={{
                  width: '100%',
                  height: '100%',
                  backgroundColor: 'black',
                }}
                ref={cameraRef}
                facing='back'
                enableTorch={enableTorch}
                onLayout={!canvasSize ? onLayout : undefined}
                onMountError={(e) => {
                  console.log(
                    'leejunhui - 🔥🔥🔥🔥🔥🔥 - onMountError',
                    e.message,
                  );
                }}
              />
            ) : null} */}
            {renderCanvas}
          </YTYStack>
        )}
        {renderBottomPanel}
      </YTYStack>
    </>
  );
}
