import type BottomSheet from '@gorhom/bottom-sheet';
import {
  FeedbackSheetModal,
  type FeedbackSheetModalRef,
} from '@jgl/biz-components';
import {
  useNavigationBarBarHeight,
  useNavigationBarHeight,
} from '@jgl/biz-func';
import { container } from '@jgl/container';
import * as Clipboard from 'expo-clipboard';
import {
  YTText,
  YTTouchable,
  YTXStack,
  YTYStack,
  YTCustomSheet,
  YTView,
} from '@bookln/cross-platform-components';
import {
  router,
  showToast,
  useRouterParams,
  useWindowDimensions,
} from '@jgl/utils';
import {
  Canvas,
  fitbox,
  Group,
  type Matrix4,
  multiply4,
  PaintStyle,
  Path,
  processTransform3d,
  rect,
  Skia,
  Image as SkiaImage,
  type SkPoint,
  useImage,
} from '@shopify/react-native-skia';
import { useGetState, useMount, useUnmount } from 'ahooks';
import {
  setAudioModeAsync,
  useAudioPlayer,
  useAudioPlayerStatus,
} from 'expo-audio';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  Image as RNImage,
  StyleSheet,
  Text,
  TouchableOpacity,
  Platform,
} from 'react-native';
import Popover from 'react-native-popover-view';
import Animated, {
  useAnimatedRef,
  useScrollViewOffset,
  useSharedValue,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  addTextTransMp3Task,
  checkTextTransMp3,
} from '../../api/TranslateServiceApi';
import { IconPause, IconPlay } from '@bookln/icon-custom';
import type { TextTranslationResultListItemDTO } from '../../dtos/TextTranslationResultDTO';
import { GestureHandler } from '../../features/takePhotoTranslation/GestureHandler';
import { JglImage, JglSpinner } from '@jgl/ui-v4';
import { Copy } from '@bookln/iconsax';
import { useTheme } from 'tamagui';

const bottomViewInitialHeight = 190;
const bottomViewMaxHeight = 540;
const TEXT_TO_AUDIO_POLL_INTERVAL = 400;
const TEXT_TO_AUDIO_POLL_TIMEOUT = 10 * 1000;
const audioControlHeight = 80;

// 判断点是否在多边形内
function pointInPolygon(point: [number, number], polygon: [number, number][]) {
  const [x, y] = point;
  let inside = false;
  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const pi = polygon[i];
    const pj = polygon[j];

    if (pi && pj) {
      const [xi, yi] = pi;
      const [xj, yj] = pj;
      const intersect =
        yi > y !== yj > y &&
        x < ((xj - xi) * (y - yi)) / (yj - yi + 0.00001) + xi;
      if (intersect) inside = !inside;
    }
  }
  return inside;
}

// 逆变换：将点击点从画布坐标变换到图片坐标
function applyInverseMatrix(
  [x, y]: [number, number],
  matrix: Matrix4,
): [number, number] {
  // The matrix is column-major from gl-matrix.
  // The 2D affine transform matrix is embedded as:
  // [ a, c, tx ]
  // [ b, d, ty ]
  //
  // react-native-skia's Matrix4 (from gl-matrix) is column-major:
  // [a, b, 0, 0,  c, d, 0, 0,  0, 0, 1, 0,  tx, ty, 0, 1]
  // where a=m[0], b=m[1], c=m[4], d=m[5], tx=m[12], ty=m[13]

  const a = matrix[0];
  const b = matrix[4];
  const c = matrix[12];
  const d = matrix[1];
  const e = matrix[5];
  const f = matrix[13];

  // Invert the matrix
  const det = a * e - b * d;
  if (Math.abs(det) < 1e-9) {
    // Not invertible
    return [x, y];
  }

  const invDet = 1 / det;

  const invA = e * invDet;
  const invB = -b * invDet;
  const invC = (b * f - c * e) * invDet;
  const invD = -d * invDet;
  const invE = a * invDet;
  const invF = (c * d - a * f) * invDet;

  // Apply the inverse matrix to the point (x, y)
  const tx = invA * x + invB * y + invC;
  const ty = invD * x + invE * y + invF;

  return [tx, ty];
}

function mapPoint(matrix: Matrix4, point: SkPoint): SkPoint {
  const x = point.x * matrix[0] + point.y * matrix[4] + matrix[12];
  const y = point.x * matrix[1] + point.y * matrix[5] + matrix[13];
  return { x, y };
}

const audioSpeedOptions = [
  { label: '0.5x', value: 0.5 },
  { label: '0.75x', value: 0.75 },
  { label: '1.0x', value: 1 },
  { label: '1.5x', value: 1.5 },
  { label: '1.75x', value: 1.75 },
  { label: '2.0x', value: 2 },
];

export default function TakePhotoTranslationResult() {
  const { data, imageUrl } = useRouterParams<{
    imageUrl: string;
    data: string;
  }>();

  const boxData = useMemo(() => {
    try {
      if (typeof data === 'string') {
        return JSON.parse(data) as TextTranslationResultListItemDTO[];
      }
      return [];
    } catch (error) {
      return [];
    }
  }, [data]);

  const [boxDataWithMp3Url, setBoxDataWithMp3Url] =
    useState<TextTranslationResultListItemDTO[]>(boxData);

  const skiaImage = useImage(imageUrl || '');

  const safeInsets = useSafeAreaInsets();
  const navigationBarHeight = useNavigationBarHeight();
  const barHeight = useNavigationBarBarHeight();

  const feedbackSheetModalRef = useRef<FeedbackSheetModalRef>(null);

  // ref
  const bottomSheetRef = useRef<BottomSheet>(null);

  const [audioSpeedPopoverVisible, setAudioSpeedPopoverVisible] =
    useState(false);
  const [audioSpeed, setAudioSpeed] = useState(1);
  const [audioLoading, setAudioLoading, getAudioLoading] = useGetState(false);
  const isMountRef = useRef(true);

  const [highlightedIndex, setHighlightedIndex, getHighlightedIndex] =
    useGetState<number | null>(null);

  const highlightedBox = useMemo(() => {
    if (!boxDataWithMp3Url || highlightedIndex === null) return null;
    return boxDataWithMp3Url[highlightedIndex];
  }, [boxDataWithMp3Url, highlightedIndex]);

  const audioPlayer = useAudioPlayer();
  const { playing, isBuffering, didJustFinish } =
    useAudioPlayerStatus(audioPlayer);
  const audioPlayedAtLeastOnceRef = useRef(false);
  const theme = useTheme();

  const isAudioPlaying = useMemo(() => {
    if (playing) {
      if (highlightedBox) {
        if (didJustFinish) {
          return false;
        } else {
          return true;
        }
      } else {
        return false;
      }
    } else {
      return false;
    }
  }, [didJustFinish, highlightedBox, playing]);

  const audioBufferingOrNot = useMemo(() => {
    if (isBuffering) {
      if (highlightedBox && audioPlayedAtLeastOnceRef.current) {
        return true;
      }
      return false;
    }
    return false;
  }, [highlightedBox, isBuffering]);

  const [isAudioBuffering, setIsAudioBuffering, getIsAudioBuffering] =
    useGetState(false);

  useEffect(() => {
    setIsAudioBuffering(audioBufferingOrNot);
  }, [audioBufferingOrNot, setIsAudioBuffering]);

  useEffect(() => {
    setAudioModeAsync({
      playsInSilentMode: true,
    });
  }, []);

  const pauseAudioPlaying = useCallback(() => {
    audioPlayer.pause();
  }, [audioPlayer]);

  const startAudioPlaying = useCallback(
    (param: { src: string }) => {
      if (!audioPlayedAtLeastOnceRef.current) {
        audioPlayedAtLeastOnceRef.current = true;
      }
      audioPlayer.replace(param.src);
      audioPlayer.play();
    },
    [audioPlayer],
  );

  const changeAudioPlaybackRate = useCallback(
    (param: { rate: number }) => {
      if (Platform.OS === 'android') {
        audioPlayer.shouldCorrectPitch = true;
        audioPlayer.setPlaybackRate(param.rate);
      } else {
        audioPlayer.setPlaybackRate(param.rate, 'high');
      }
    },
    [audioPlayer],
  );

  // const {
  //   audioSrc,
  //   isBuffering: isAudioBuffering,
  //   isPlay: isAudioPlaying,
  //   play: startAudioPlaying,
  //   stop: stopAudioPlaying,
  //   changePlaybackRate: changeAudioPlaybackRate,
  // } = useAudio();

  const onPressNavBack = useCallback(() => {
    router.back();
  }, []);

  const onPressFeedback = useCallback(() => {
    feedbackSheetModalRef.current?.present();
  }, []);

  const handleDismissFeedbackModal = useCallback(() => {
    feedbackSheetModalRef.current?.dismiss();
  }, []);

  const windowSize = useWindowDimensions();
  const canvasWidth = useMemo(() => windowSize.width, [windowSize.width]);
  const canvasHeight = useMemo(() => {
    return (
      windowSize.height -
      (safeInsets.top + navigationBarHeight + barHeight) -
      (safeInsets.bottom + bottomViewInitialHeight)
    );
  }, [
    barHeight,
    navigationBarHeight,
    safeInsets.bottom,
    safeInsets.top,
    windowSize.height,
  ]);

  // 视图坐标系尺寸
  const imageWidth = useMemo(() => canvasWidth, [canvasWidth]);
  const imageHeight = useMemo(() => canvasHeight, [canvasHeight]);

  const [imageSize, setImageSize] = useState<{
    width: number;
    height: number;
  } | null>(null);

  // 获取图片原始宽高
  useEffect(() => {
    if (!imageUrl) return;
    RNImage.getSize(imageUrl, (w, h) => setImageSize({ width: w, height: h }));
  }, [imageUrl]);

  // 初始变换矩阵：从 内容坐标系 -> 视图坐标系
  const initialMatrix = useMemo(() => {
    if (!imageSize) return Skia.Matrix() as unknown as Matrix4;
    const src = rect(0, 0, imageSize.width, imageSize.height);
    const dst = rect(0, 0, imageWidth, imageHeight);
    const m4 = processTransform3d(fitbox('fill', src, dst));
    return m4;
  }, [imageSize, imageWidth, imageHeight]);

  // 安卓平台修正矩阵
  const androidFixMatrix = useMemo(() => {
    if (Platform.OS === 'android') {
      return processTransform3d([{ scale: 1 }]);
    }
    return processTransform3d([]); // iOS 返回单位矩阵
  }, []);

  const currentScale = useSharedValue(1);
  // 手势矩阵
  const gestureMatrix = useSharedValue(processTransform3d([]));

  const processedPaths = useMemo(() => {
    if (!boxDataWithMp3Url || !initialMatrix) return [];
    // 预先组合好变换矩阵，提高效率
    const transformMatrix = multiply4(androidFixMatrix, initialMatrix);
    return boxDataWithMp3Url.map((item) => {
      const points = item.box as [number, number][];
      if (!points || points.length < 3) return Skia.Path.Make();
      const path = Skia.Path.Make();
      const mappedPoints = points.map(([x, y]) =>
        mapPoint(transformMatrix, { x, y }),
      );
      path.moveTo(mappedPoints[0]?.x ?? 0, mappedPoints[0]?.y ?? 0);
      for (let i = 1; i < mappedPoints.length; i++) {
        const point = mappedPoints[i];
        path.lineTo(point?.x ?? 0, point?.y ?? 0);
      }
      path.close();
      return path;
    });
  }, [boxDataWithMp3Url, initialMatrix, androidFixMatrix]);

  const audioControlLeftButtonDisabled = useMemo(() => {
    return highlightedIndex === 0 || highlightedIndex === null;
  }, [highlightedIndex]);

  const audioControlRightButtonDisabled = useMemo(() => {
    return (
      highlightedIndex === boxDataWithMp3Url.length - 1 ||
      highlightedIndex === null
    );
  }, [highlightedIndex, boxDataWithMp3Url]);

  const checkTextTransTaskResult = useCallback(async (recordId: string) => {
    const request = checkTextTransMp3({ recordId });
    const response = await container.net().fetch(request);
    if (response.success && response.data) {
      console.log('🚀 ~ checkTextTransTaskResult ~ response:', response.data);
      return response.data;
    } else {
      return null;
    }
  }, []);

  const startPollingTextToAudioTaskResult = useCallback(
    async (param: { recordId: string; itemIndex: number }) => {
      const { recordId, itemIndex } = param;
      let timeout: ReturnType<typeof setTimeout>;
      let interval: ReturnType<typeof setInterval>;
      interval = setInterval(async () => {
        if (!isMountRef.current) {
          clearInterval(interval);
          clearTimeout(timeout);
          return;
        }
        console.log(
          'leejunhui - 🔥🔥🔥🔥🔥🔥 - startPollingTextToAudioTask',
          recordId,
        );
        const result = await checkTextTransTaskResult(recordId);
        if (result) {
          clearInterval(interval);
          clearTimeout(timeout);
          setAudioLoading(false);
          if (itemIndex !== getHighlightedIndex()) {
            return;
          }
          setBoxDataWithMp3Url((prev) => {
            return prev.map((item, index) => {
              if (index === itemIndex) {
                return { ...item, mp3Url: result };
              }
              return item;
            });
          });
          startAudioPlaying({ src: result });
        }
      }, TEXT_TO_AUDIO_POLL_INTERVAL);
      timeout = setTimeout(() => {
        clearInterval(interval);
        showToast({
          title: '处理超时',
        });
      }, TEXT_TO_AUDIO_POLL_TIMEOUT);
    },
    [
      checkTextTransTaskResult,
      getHighlightedIndex,
      setAudioLoading,
      startAudioPlaying,
    ],
  );

  const submitTextToAudioTask = useCallback(
    async (param: { itemIndex: number; content: string }) => {
      const { itemIndex, content } = param;
      const request = addTextTransMp3Task({
        content,
      });
      setAudioLoading(true);
      const response = await container.net().fetch(request);
      if (response.success && response.data) {
        const recordId = response.data;
        console.log('submitTextToAudioTask - response', response.data);
        // 开启轮询
        startPollingTextToAudioTaskResult({ recordId, itemIndex });
      } else {
        setAudioLoading(false);
        showToast({
          title: '处理失败',
        });
        // TODO: chenfeng - 2025-07-15 埋点
      }
    },
    [setAudioLoading, startPollingTextToAudioTaskResult],
  );

  const onPressAudioPlayButton = useCallback(() => {
    if (isAudioBuffering) {
      return;
    }

    if (isAudioPlaying) {
      pauseAudioPlaying();
      return;
    }

    let targetIndex = -1;
    const latestHighlightedIndex = getHighlightedIndex();
    if (latestHighlightedIndex === null) {
      setHighlightedIndex(0);
      targetIndex = 0;
    } else {
      targetIndex = latestHighlightedIndex;
    }

    const targetBox = boxDataWithMp3Url[targetIndex];
    if (!targetBox) return;
    if (targetBox?.mp3Url) {
      startAudioPlaying({ src: targetBox.mp3Url });
    } else {
      // 发起文本转语音任务
      submitTextToAudioTask({
        itemIndex: targetIndex,
        content: `${targetBox.ori_text}`,
      });
    }
  }, [
    boxDataWithMp3Url,
    getHighlightedIndex,
    isAudioBuffering,
    isAudioPlaying,
    setHighlightedIndex,
    startAudioPlaying,
    pauseAudioPlaying,
    submitTextToAudioTask,
  ]);

  const playButtonStyle = useMemo(() => {
    if (audioLoading || isAudioBuffering) {
      return {
        padding: 20,
      };
    } else {
      if (isAudioPlaying) {
        return {
          padding: 20,
        };
      } else {
        return {
          paddingTop: 15,
          paddingRight: 14,
          paddingBottom: 15,
          paddingLeft: 16,
        };
      }
    }
  }, [audioLoading, isAudioBuffering, isAudioPlaying]);

  // 抽取公共逻辑：切换到指定 index 并播放/请求音频
  const playOrRequestAudioAtIndex = useCallback(
    (newIndex: number) => {
      setHighlightedIndex(newIndex);
      const targetBox = boxDataWithMp3Url[newIndex];
      console.log('playOrRequestAudioAtIndex: targetBox:', targetBox);
      if (!targetBox) {
        console.log('playOrRequestAudioAtIndex: targetBox 不存在，return');
        return;
      }
      if (targetBox.mp3Url) {
        console.log(
          'playOrRequestAudioAtIndex: 直接播放mp3Url:',
          targetBox.mp3Url,
        );
        startAudioPlaying({ src: targetBox.mp3Url });
      } else {
        console.log(
          'playOrRequestAudioAtIndex: mp3Url不存在，发起文本转语音任务，内容:',
          targetBox.ori_text,
        );
        submitTextToAudioTask({
          itemIndex: newIndex,
          content: `${targetBox.ori_text}`,
        });
      }
    },
    [
      boxDataWithMp3Url,
      setHighlightedIndex,
      startAudioPlaying,
      submitTextToAudioTask,
    ],
  );

  // 点击处理
  const handleCanvasTap = useCallback(
    (pt: { x: number; y: number }) => {
      if (!imageSize || !boxDataWithMp3Url?.length) return;
      // 完整的变换矩阵
      const tempMatrix = multiply4(androidFixMatrix, initialMatrix);
      const totalMatrix = multiply4(gestureMatrix.value, tempMatrix);
      // 逆变换，将屏幕坐标转换为原始图片坐标
      const [imgX, imgY] = applyInverseMatrix([pt.x, pt.y], totalMatrix);
      const scale = currentScale.value;
      const scaledImgX = imgX * scale;
      const scaledImgY = imgY * scale;
      // 使用原始图片坐标进行命中判断
      const hitIdx = boxDataWithMp3Url.findIndex((item) => {
        const poly = item.box as [number, number][];
        if (!poly) return false;
        return pointInPolygon([scaledImgX, scaledImgY], poly);
      });
      if (hitIdx !== -1) {
        setHighlightedIndex(hitIdx);

        // 增加调试日志
        console.log(
          '[handleCanvasTap] isAudioPlaying:',
          isAudioPlaying,
          'hitIdx:',
          hitIdx,
        );
        if (isAudioPlaying) {
          console.log('[handleCanvasTap] 正在播放音频，先暂停');
          pauseAudioPlaying();
          playOrRequestAudioAtIndex(hitIdx);
        } else {
          if (getAudioLoading()) {
            setAudioLoading(false);
          }
          if (getIsAudioBuffering()) {
            setIsAudioBuffering(false);
          }
          playOrRequestAudioAtIndex(hitIdx);
        }
      } else {
        if (getHighlightedIndex() !== null) {
          return;
        }
        setHighlightedIndex(null); // 如果没点中，取消高亮
      }
    },
    [
      imageSize,
      boxDataWithMp3Url,
      androidFixMatrix,
      initialMatrix,
      gestureMatrix.value,
      currentScale.value,
      setHighlightedIndex,
      isAudioPlaying,
      pauseAudioPlaying,
      playOrRequestAudioAtIndex,
      getAudioLoading,
      getIsAudioBuffering,
      setAudioLoading,
      setIsAudioBuffering,
      getHighlightedIndex,
    ],
  );

  const handlePressPlayPreviousAudio = useCallback(() => {
    console.log('handlePressPlayPreviousAudio');
    if (highlightedIndex === null) return;
    const newIndex = highlightedIndex - 1;
    if (newIndex < 0) return;
    setHighlightedIndex(newIndex);
    if (isAudioPlaying) {
      console.log(
        'audioControlLeftButton onPress: 切换到上一个音频，当前highlightedIndex:',
        highlightedIndex,
        '新index:',
        newIndex,
      );
      pauseAudioPlaying();
      playOrRequestAudioAtIndex(newIndex);
    } else {
      let needPlay = false;
      if (getAudioLoading()) {
        needPlay = true;
        setAudioLoading(false);
      }
      if (getIsAudioBuffering()) {
        needPlay = true;
        setIsAudioBuffering(false);
      }
      if (needPlay) {
        playOrRequestAudioAtIndex(newIndex);
      } else {
        setHighlightedIndex(newIndex);
      }
    }
  }, [
    getAudioLoading,
    getIsAudioBuffering,
    highlightedIndex,
    isAudioPlaying,
    pauseAudioPlaying,
    playOrRequestAudioAtIndex,
    setAudioLoading,
    setHighlightedIndex,
    setIsAudioBuffering,
  ]);

  const handlePressPlayNextAudio = useCallback(() => {
    console.log('handlePressPlayNextAudio');
    if (highlightedIndex === null) return;
    const newIndex = highlightedIndex + 1;
    if (newIndex >= boxDataWithMp3Url.length) return;

    // 优先处理播放/加载/缓冲状态
    if (isAudioPlaying) {
      console.log(
        'audioControlRightButton onPress: 切换到下一个音频，当前highlightedIndex:',
        highlightedIndex,
        '新index:',
        newIndex,
      );
      pauseAudioPlaying();
      playOrRequestAudioAtIndex(newIndex);
    } else {
      let needPlay = false;
      if (getAudioLoading()) {
        needPlay = true;
        setAudioLoading(false);
      }
      if (getIsAudioBuffering()) {
        needPlay = true;
        setIsAudioBuffering(false);
      }
      if (needPlay) {
        playOrRequestAudioAtIndex(newIndex);
      } else {
        setHighlightedIndex(newIndex);
      }
    }
  }, [
    highlightedIndex,
    boxDataWithMp3Url.length,
    isAudioPlaying,
    pauseAudioPlaying,
    playOrRequestAudioAtIndex,
    getAudioLoading,
    getIsAudioBuffering,
    setAudioLoading,
    setIsAudioBuffering,
    setHighlightedIndex,
  ]);

  const renderAudioControl = useMemo(() => {
    return (
      <YTYStack
        position='absolute'
        bottom={0}
        left={0}
        right={0}
        w={'$full'}
        h={audioControlHeight + safeInsets.bottom}
        bg='white'
      >
        <YTXStack
          gap={32}
          ai='center'
          jc='center'
          w='$full'
          h={audioControlHeight}
          bg='white'
        >
          <YTTouchable
            onPress={handlePressPlayPreviousAudio}
            disabled={audioControlLeftButtonDisabled}
          >
            <JglImage
              source={
                audioControlLeftButtonDisabled
                  ? require('../../assets/images/ic_prev_disabled.png')
                  : require('../../assets/images/ic_prev.png')
              }
              w={34}
              h={34}
            />
          </YTTouchable>
          <YTTouchable
            onPress={onPressAudioPlayButton}
            bg='#4E76FF'
            w={54}
            h={54}
            borderRadius={100}
            style={playButtonStyle}
          >
            <YTView
              w={'$full'}
              h={'$full'}
              bg={'transparent'}
              alignItems='center'
              justifyContent='center'
            >
              {audioLoading || isAudioBuffering ? (
                <JglSpinner isLoading size='small' color='white' />
              ) : isAudioPlaying ? (
                <IconPause />
              ) : (
                <IconPlay />
              )}
            </YTView>
          </YTTouchable>
          <YTTouchable
            onPress={handlePressPlayNextAudio}
            disabled={audioControlRightButtonDisabled}
          >
            <JglImage
              source={
                audioControlRightButtonDisabled
                  ? require('../../assets/images/ic_next_disabled.png')
                  : require('../../assets/images/ic_next.png')
              }
              w={34}
              h={34}
            />
          </YTTouchable>
          <Popover
            isVisible={audioSpeedPopoverVisible}
            onRequestClose={() => setAudioSpeedPopoverVisible(false)}
            backgroundStyle={{
              backgroundColor: 'transparent',
            }}
            popoverStyle={{
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 5,
              },
              shadowOpacity: 0.15,
              shadowRadius: 10,
              borderRadius: 8,
            }}
            from={
              <TouchableOpacity
                style={{
                  position: 'absolute',
                  right: 24,
                  alignSelf: 'center',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
                hitSlop={{
                  top: 16,
                  bottom: 16,
                  left: 16,
                  right: 16,
                }}
                onPress={() => setAudioSpeedPopoverVisible(true)}
              >
                <YTText fontSize={14} color='$color9'>
                  倍速
                </YTText>
              </TouchableOpacity>
            }
          >
            <YTYStack px={8} py={4} bg='white'>
              {audioSpeedOptions.map((item) => {
                const isSelected = audioSpeed === item.value;
                return (
                  <React.Fragment key={item.value}>
                    <YTTouchable
                      onPress={() => {
                        setAudioSpeed(item.value);
                        setAudioSpeedPopoverVisible(false);
                        changeAudioPlaybackRate({ rate: item.value });
                      }}
                      bg={isSelected ? '#4E76FF' : 'transparent'}
                      borderRadius={6}
                      px={16}
                      py={8}
                    >
                      <YTText
                        fontSize={14}
                        color={isSelected ? '$color1' : '$color9'}
                      >
                        {item.label}
                      </YTText>
                    </YTTouchable>
                  </React.Fragment>
                );
              })}
            </YTYStack>
          </Popover>
        </YTXStack>
        <YTView h={safeInsets.bottom} bg='transparent' w={'$full'} />
      </YTYStack>
    );
  }, [
    safeInsets.bottom,
    handlePressPlayPreviousAudio,
    audioControlLeftButtonDisabled,
    onPressAudioPlayButton,
    playButtonStyle,
    audioLoading,
    isAudioBuffering,
    isAudioPlaying,
    handlePressPlayNextAudio,
    audioControlRightButtonDisabled,
    audioSpeedPopoverVisible,
    audioSpeed,
    changeAudioPlaybackRate,
  ]);

  const maxHeightForBottomView = useMemo(
    () => (bottomViewMaxHeight / windowSize.height) * 100,
    [windowSize.height],
  );

  const animatedRef = useAnimatedRef<Animated.ScrollView>();
  const scrollOffset = useScrollViewOffset(animatedRef);

  const onPressCopyOriginalText = useCallback(
    async (originalContent: string) => {
      await Clipboard.setStringAsync(originalContent);
      setTimeout(() => {
        showToast({
          title: '复制原文成功',
        });
      }, 100);
    },
    [],
  );

  const onPressCopyTransText = useCallback(async (transText: string) => {
    await Clipboard.setStringAsync(transText);
    setTimeout(() => {
      showToast({
        title: '复制译文成功',
      });
    }, 100);
  }, []);

  const renderBottomViewTextContent = useMemo(() => {
    if (!highlightedBox) {
      return (
        <YTYStack px={16} py={8} bg='transparent'>
          <Text
            style={{
              fontSize: 14,
              lineHeight: 22,
              color: '#121212',
              overflow: 'scroll',
            }}
            selectable
          >
            点击选框播放读音
          </Text>
        </YTYStack>
      );
    } else {
      return (
        <>
          <YTYStack bg='transparent'>
            <YTYStack
              pt={8}
              pb={12}
              px={16}
              bg='transparent'
              ai='flex-end'
              gap={8}
            >
              <Text
                style={{
                  fontSize: 14,
                  lineHeight: 22,
                  color: '#121212',
                  overflow: 'scroll',
                  flex: 1,
                }}
                selectable
              >
                {highlightedBox.ori_text}
              </Text>
              <YTTouchable
                w={22}
                h={22}
                ai='center'
                jc='center'
                borderRadius={6}
                bg='$gray3'
                onPress={() => onPressCopyOriginalText(highlightedBox.ori_text)}
              >
                <Copy variant='Bold' size={13.75} color={theme.gray8.val} />
              </YTTouchable>
            </YTYStack>
            <YTYStack
              pt={8}
              pb={12}
              px={16}
              bg='transparent'
              ai='flex-end'
              gap={8}
            >
              <Text
                style={{
                  fontSize: 14,
                  lineHeight: 22,
                  color: '#121212',
                  overflow: 'scroll',
                  flex: 1,
                }}
                selectable
              >
                {highlightedBox.text}
              </Text>
              <YTTouchable
                w={22}
                h={22}
                ai='center'
                jc='center'
                borderRadius={6}
                bg='$gray3'
                onPress={() => onPressCopyTransText(highlightedBox.text)}
              >
                <Copy variant='Bold' size={13.75} color={theme.gray8.val} />
              </YTTouchable>
            </YTYStack>
          </YTYStack>
          <YTText mt={16} fontSize={12} color={'$color8'} alignSelf='center'>
            以上内容由AI生成
          </YTText>
        </>
      );
    }
  }, [
    highlightedBox,
    onPressCopyOriginalText,
    onPressCopyTransText,
    theme.gray8.val,
  ]);

  const renderBottomView = useMemo(() => {
    return (
      <YTCustomSheet
        initialHeight={(bottomViewInitialHeight / windowSize.height) * 100}
        minHeight={(bottomViewInitialHeight / windowSize.height) * 100}
        maxHeight={maxHeightForBottomView}
        visible={true}
        rightCloseButton={'none'}
        showBackdrop={false}
        dragHandlerBackgroundColor='white'
        fullSheetDraggable
        contentScrollOffset={scrollOffset}
      >
        <Animated.ScrollView
          ref={animatedRef}
          style={{
            width: '100%',
            height: '100%',
            backgroundColor: 'white',
          }}
          scrollEventThrottle={16}
          contentContainerStyle={{
            backgroundColor: 'white',
          }}
          alwaysBounceVertical
        >
          <YTYStack
            w='$full'
            h='$full'
            position='relative'
            bg='white'
            overflow='scroll'
            pb={audioControlHeight + safeInsets.bottom}
          >
            {renderBottomViewTextContent}
          </YTYStack>
        </Animated.ScrollView>
        {renderAudioControl}
      </YTCustomSheet>
    );
  }, [
    animatedRef,
    maxHeightForBottomView,
    renderAudioControl,
    renderBottomViewTextContent,
    safeInsets.bottom,
    scrollOffset,
    windowSize.height,
  ]);

  useUnmount(() => {
    isMountRef.current = false;
  });

  useMount(() => {
    isMountRef.current = true;
  });

  const cornerPathEffect = useMemo(() => Skia.PathEffect.MakeCorner(8), []);

  const headerRight = useCallback(() => {
    return (
      <YTTouchable
        px={12}
        py={6}
        borderRadius={100}
        bg='#00000033'
        onPress={onPressFeedback}
      >
        <YTText fontSize={14} lineHeight={22} color='white'>
          反馈
        </YTText>
      </YTTouchable>
    );
  }, [onPressFeedback]);

  const renderNavBar = useMemo(() => {
    return (
      <YTXStack
        position='fixed'
        top={0}
        left={0}
        right={0}
        zIndex={1000}
        w='$full'
        h={safeInsets.top + navigationBarHeight}
        ai='center'
      >
        <YTXStack
          position='absolute'
          w='$full'
          left={0}
          right={0}
          top={safeInsets.top}
          h={Platform.OS === 'ios' ? barHeight : undefined}
          py={10}
          px={16}
          jc='space-between'
          ai='center'
        >
          <YTTouchable
            ai='center'
            jc='center'
            onPress={onPressNavBack}
            style={{
              width: 32,
              height: 32,
            }}
          >
            <JglImage
              source={require('../../assets/images/icon_back_white_bg_black_line.png')}
              w={32}
              h={32}
            />
          </YTTouchable>
          <YTTouchable
            px={12}
            h={32}
            borderRadius={100}
            bg='#00000033'
            onPress={onPressFeedback}
          >
            <YTText fontSize={14} color='white'>
              反馈
            </YTText>
          </YTTouchable>
        </YTXStack>
      </YTXStack>
    );
  }, [
    barHeight,
    navigationBarHeight,
    onPressFeedback,
    onPressNavBack,
    safeInsets.top,
  ]);

  // 5. 渲染
  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar style='dark' />
      <YTYStack flex={1} bg='black' position='relative'>
        {renderNavBar}
        <YTYStack flex={1}>
          <Canvas
            style={{
              width: canvasWidth,
              height: canvasHeight,
            }}
          >
            <Group matrix={gestureMatrix}>
              <SkiaImage
                image={skiaImage}
                x={0}
                y={0}
                width={imageWidth}
                height={imageHeight}
                fit='fill'
              />
              {/* 绘制预处理后的 Path */}
              {processedPaths.map((path, idx) => {
                const key =
                  (boxDataWithMp3Url[idx]?.ori_text || 'box') +
                  '-' +
                  idx.toString();

                const fillPaint = Skia.Paint();
                fillPaint.setStyle(PaintStyle.Fill);
                fillPaint.setColor(
                  Skia.Color(
                    highlightedIndex === idx
                      ? '#003AFF1A'
                      : 'rgba(86, 86, 86, 0.15)',
                  ),
                );
                fillPaint.setPathEffect(cornerPathEffect);

                return (
                  <React.Fragment key={key}>
                    <Path path={path} paint={fillPaint} />
                    {/* <Path path={path} paint={strokePaint} /> */}
                  </React.Fragment>
                );
              })}
            </Group>
          </Canvas>
          <GestureHandler
            matrix={gestureMatrix}
            size={{
              x: 0,
              y: 0,
              width: imageWidth,
              height: imageHeight,
            }}
            currentScale={currentScale}
            canvasWidth={canvasWidth}
            canvasHeight={canvasHeight}
            onTap={handleCanvasTap}
          />
        </YTYStack>
        {renderBottomView}
      </YTYStack>
      <FeedbackSheetModal
        ref={feedbackSheetModalRef}
        screenshot={imageUrl}
        onDismiss={handleDismissFeedbackModal}
      />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 24,
    backgroundColor: 'grey',
  },
  contentContainer: {
    flex: 1,
    alignItems: 'center',
  },
  itemContainer: {
    padding: 6,
    margin: 6,
    backgroundColor: '#eee',
  },
});
