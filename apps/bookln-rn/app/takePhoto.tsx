import {
  PermissionEnum,
  PermissionHooks,
  PermissionPurposeScene,
} from '@bookln/permission';
import { jglAiQAInputToolBarImageAtom, eventBus } from '@jgl/ai-qa-v2';
import {
  useNavigationBarBarHeight,
  useNavigationBarHeight,
  useSafeAreaInsets,
} from '@jgl/biz-func';
import { container } from '@jgl/container';
import Toast from 'react-native-root-toast';
import { OSSUploadNative } from '@jgl/upload/OSSUpload';
import { showToast } from '@jgl/utils';
import { CameraView } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import { router, Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useSetAtom } from 'jotai';
import { useCallback, useMemo, useRef, useState } from 'react';
import { Platform } from 'react-native';
import {
  YTImage,
  YTTouchable,
  YTXStack,
  YTYStack,
} from '@bookln/cross-platform-components';

export default function TakePhoto() {
  const safeInsets = useSafeAreaInsets();
  const navigationBarHeight = useNavigationBarHeight();
  const barHeight = useNavigationBarBarHeight();

  const cameraRef = useRef<CameraView>(null);
  const [enableTorch, setEnableTorch] = useState<boolean>(false);
  const { checkAndRequestPermission } = PermissionHooks.usePermission();
  const setImage = useSetAtom(jglAiQAInputToolBarImageAtom);
  const [isUploading, setIsUploading] = useState(false);
  const [selectedPhotoUri, setSelectedPhotoUri] = useState<string | null>(null);

  const onCommitPhoto = useCallback(
    async (param: {
      url: string;
      /**
       * 图片大小 in Bytes
       */
      size: number;
      width: number;
      height: number;
    }) => {
      const { url, width, height } = param;
      const toast = Toast.show('上传中...', {
        duration: Toast.durations.LONG,
        position: Toast.positions.CENTER,
      });
      setIsUploading(true);
      const imageUrl = await OSSUploadNative.uploadToOSSPermanently({
        tmpPath: url,
        options: {
          net: container.net(),
        },
        bizCode: 'bookln',
        imageDimensions: { width, height },
      });
      if (imageUrl) {
        console.log('JglAiQATakePhotoComponent - url', url, imageUrl);
        setImage({
          url: imageUrl,
          size: param.size,
          width,
          height,
        });
        Toast.hide(toast);
        router.back();
      } else {
        showToast({
          title: '上传失败',
        });
      }
    },
    [setImage],
  );

  const onPressTakePhoto = useCallback(async () => {
    if (cameraRef.current) {
      if (isUploading) {
        return;
      }

      try {
        const result = await cameraRef.current.takePictureAsync({
          base64: false,
          quality: 1,
        });
        console.log('take Photo result', JSON.stringify(result));

        if (result) {
          setSelectedPhotoUri(result.uri);

          eventBus.emit('takePhoto', {
            url: result.uri,
            width: result.width,
            height: result.height,
          });

          router.back();
          // onCommitPhoto({
          //   url: result.uri,
          //   width: result.width,
          //   height: result.height,
          //   size: 0,
          // });
        }
      } catch (error) {
        console.log('take Photo error', JSON.stringify(error));
      }
    }
  }, [isUploading]);

  const onPressNavBack = useCallback(() => {
    if (isUploading) {
      return;
    }
    router.back();
  }, [isUploading]);

  const renderNavBar = useMemo(() => {
    return (
      <YTXStack
        position='absolute'
        top={0}
        left={0}
        right={0}
        zIndex={1000}
        w='$full'
        h={safeInsets.top + navigationBarHeight}
        ai='center'
        bg='transparent'
      >
        <YTXStack
          position='absolute'
          w='$full'
          left={0}
          right={0}
          top={safeInsets.top}
          h={Platform.OS === 'ios' ? barHeight : undefined}
          py={10}
          px={16}
          jc='space-between'
          ai='center'
          bg='transparent'
        >
          <YTTouchable
            ai='center'
            jc='center'
            onPress={onPressNavBack}
            style={{
              width: 32,
              height: 32,
            }}
          >
            <YTImage
              source={require('../assets/images/icon_back_white_bg_black_line.png')}
              w={32}
              h={32}
            />
          </YTTouchable>
        </YTXStack>
      </YTXStack>
    );
  }, [barHeight, navigationBarHeight, onPressNavBack, safeInsets.top]);

  const onPressOpenAlbum = useCallback(async () => {
    console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - onPressOpenAlbum');
    if (isUploading) {
      return;
    }

    const authResult = await checkAndRequestPermission({
      permission: PermissionEnum.Album,
      scene: PermissionPurposeScene.ChoicePicture,
    });
    if (authResult) {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        aspect: [4, 3],
        quality: 1,
        selectionLimit: 1,
      });
      console.log('🚀 ~ handlePickPhoto ~ result:', result);
      if (!result.canceled) {
        if (result.assets.length > 0) {
          const asset = result.assets[0];
          if (asset) {
            const { uri, width, height } = asset;
            if (uri) {
              eventBus.emit('choosePhotoLibraryImage', {
                url: uri,
                width,
                height,
              });
              router.back();
              // onCommitPhoto({
              //   url: uri,
              //   width,
              //   height,
              //   size: 0,
              // });
            }
          }
        }
      }
    }
  }, [checkAndRequestPermission, isUploading]);

  const renderBottomPanel = useMemo(() => {
    return (
      <YTXStack
        position='fixed'
        w='$full'
        left={0}
        right={0}
        bottom={0}
        px={24}
        pb={safeInsets.bottom}
        h={142 + safeInsets.bottom}
        ai='center'
        justifyContent='space-between'
        bg='transparent'
      >
        <YTTouchable
          w={44}
          h={44}
          bg='#262626'
          ai='center'
          jc='center'
          borderRadius={9999}
          onPress={onPressOpenAlbum}
        >
          <YTImage
            source={require('../assets/images/ic_open_album.png')}
            w={24}
            h={24}
          />
        </YTTouchable>
        <YTTouchable
          w={64}
          h={64}
          bg='#262626'
          ai='center'
          jc='center'
          borderRadius={9999}
          onPress={onPressTakePhoto}
        >
          <YTImage
            source={require('../assets/images/ic_take_photo_btn.png')}
            w={64}
            h={64}
          />
        </YTTouchable>
        <YTTouchable
          w={44}
          h={44}
          bg={enableTorch === false ? '#262626' : 'white'}
          ai='center'
          jc='center'
          borderRadius={9999}
          onPress={() => setEnableTorch((prev) => !prev)}
        >
          <YTImage
            source={
              enableTorch === false
                ? require('../assets/images/ic_flash_light_off.png')
                : require('../assets/images/ic_flash_light_on.png')
            }
            w={24}
            h={24}
          />
        </YTTouchable>
      </YTXStack>
    );
  }, [enableTorch, onPressOpenAlbum, onPressTakePhoto, safeInsets.bottom]);

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar style='light' />
      <YTYStack flex={1} bg='black' position='relative'>
        {renderNavBar}
        {selectedPhotoUri ? (
          <YTImage
            flex={1}
            resizeMode='contain'
            source={selectedPhotoUri}
            w='$full'
            h='$full'
          />
        ) : (
          <CameraView
            className='w-full flex-1'
            style={{ width: '100%', height: '100%', backgroundColor: 'black' }}
            ref={cameraRef}
            facing='back'
            enableTorch={enableTorch}
            onMountError={(e) => {
              console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - onMountError', e.message);
            }}
          />
        )}
        {renderBottomPanel}
      </YTYStack>
    </>
  );
}
