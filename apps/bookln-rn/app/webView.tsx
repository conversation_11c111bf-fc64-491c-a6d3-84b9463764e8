import { useWechat } from '@bookln/bookln-biz/src/login/hook/useWechat';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import {
  AppMessageType,
  type H5ActionPayload,
  H5MessageType,
  guluBallCountAtom,
  isPlatform,
  useAppSelector,
  routerMap as JglRouterMap,
  useBizRouter,
  useSafeAreaInsets,
  useWeChatIsInstalled,
} from '@jgl/biz-func';
import { JGLWebView, type JGLWebViewRef } from '@jgl/components';
import Icon from '@jgl/icon';
import {
  JglText,
  JglTouchable,
  JglView,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import {
  envVars,
  featureToggles,
  isAndroid,
  router,
  useRouterParams,
} from '@jgl/utils';
import { appendQuery } from '@yunti-private/utils-universal';
import { useUnmount } from 'ahooks';
import { Image } from 'expo-image';
import { Stack, useFocusEffect, useNavigation } from 'expo-router';
import {
  OrientationLock,
  lockAsync,
  unlockAsync,
} from 'expo-screen-orientation';
import { StatusBar } from 'expo-status-bar';
import { useSetAtom } from 'jotai';
import { NativeWechatConstants, shareMiniProgram } from 'native-wechat';
import { useCallback, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { Linking, Platform, StyleSheet } from 'react-native';
import { isTablet } from 'react-native-device-info';
import type { HapticFeedbackTypes } from 'react-native-haptic-feedback';
import type { WebViewMessageEvent, WebViewProps } from 'react-native-webview';
import type {
  WebViewHttpErrorEvent,
  WebViewNavigation,
} from 'react-native-webview/lib/WebViewTypes';
import { useAppVersionAndVid } from '../hooks/useAppVersionAndVid';
import { hapticFeedback } from '../utils/HapticFeedback';
import { isWeChatUrl } from '../utils/UrlUtil';
import { WebviewFrom } from '../utils/constants';
import { IconClose } from '@bookln/icon-custom';
import {
  pushToNativeScreenByUrl,
  pushToScanSearchScreen,
} from '../utils/WebViewHelper';
import { routerMap } from '../utils/routerMap';
import { useNotificationStore } from '../hooks/useNotificationStore';
import { NotificationType } from '../hooks/useNotificationStore.type';
import {
  PermissionEnum,
  PermissionHooks,
  PermissionPurposeScene,
} from '@bookln/permission';
import { AnimatedText } from 'react-native-reanimated/lib/typescript/component/Text';
import Animated from 'react-native-reanimated';

// 注入的 JavaScript 代码，监听 title 变化
const injectedJavaScript = `
          window.booklnApp = {
          /**
           * 扫描二维码
           * @param options
           */
            scanQRCode: function(options) {
              return new Promise((resolve, reject) => {
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: '${H5MessageType.scanQRCodeRequest}',
                  data: options
                }));
                // 监听 RN 返回的结果
                const eventFrom = '${Platform.OS}' === 'ios'? window : document;
                eventFrom.addEventListener('message', function(e) {
                  const {type,data} = JSON.parse(e.data);
                  const isScanCodeSuccess = type === '${AppMessageType.ScanCodeSuccess}';
                  if(isScanCodeSuccess){
                    resolve(data);
                  }
                });
              });
            },
            // 跳转原生页面
            push: function(url) {
              window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: '${H5MessageType.pushNativeScreen}',
                  data: {url}
                }));
            }
          };

          // 初始发送当前 title
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: '${H5MessageType.UpdateH5Title}',
              data: {title: document.title}
          }));
          
          // 使用 MutationObserver 监听 title 变化
          const titleObserver = new MutationObserver(() => {
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: '${H5MessageType.UpdateH5Title}',
              data: {title: document.title}
            }));
          });
          
          // 监听 document.title 变化
          titleObserver.observe(
            document.querySelector('title'),
            { subtree: true, characterData: true, childList: true }
          );
          
          // 也可以定时检查 title 变化
          setInterval(() => {
            window.ReactNativeWebView.postMessage(JSON.stringify({
             type: '${H5MessageType.UpdateH5Title}',
              data: {title: document.title}
            }));
          }, 1000);
          
          true;
        `;

export default function WebViewScreen() {
  const params = useRouterParams();
  console.log('🚀 ~ WebViewScreen ~ params:', params);
  const {
    url,
    title: titleFromRouter,
    webviewNeedSafeBottom,
    disableScroll = 'false',
    supportLogin = 'true',
    supportScreenRotation = 'false',
    from = WebviewFrom.OTHER,
  } = params;

  const [title, setTitle] = useState<string | undefined>(titleFromRouter);

  const [isWaitBuy, setIsWaitBuy] = useState<boolean>(false);

  const userInfo = useAppSelector((state) => state.userInfo);
  const { message, clearNotification } = useNotificationStore();
  const navigation = useNavigation();
  const safeAreaInsets = useSafeAreaInsets();
  const { checkAndRequestPermission } = PermissionHooks.usePermission();
  const webViewRef = useRef<JGLWebViewRef>(null);
  const [capableBackToPreviousWebPage, setCapableBackToPreviousWebPage] =
    useState<boolean>(false);
  const setGuluBallCountAtom = useSetAtom(guluBallCountAtom);
  const { appVid } = useAppVersionAndVid();
  const bizRouter = useBizRouter();
  const [showUseWechatScanTip, setShowUseWechatScanTip] =
    useState<boolean>(false);

  const isWeChatInstalled = useWeChatIsInstalled();

  const { launchMiniProgram } = useWechat();

  useLayoutEffect(() => {
    if (supportScreenRotation === 'true') {
      unlockAsync();
    }
  }, [supportScreenRotation]);

  useUnmount(() => {
    if (supportScreenRotation === 'true') {
      const isAndroidAndTablet = isAndroid() && isTablet();
      if (!isAndroidAndTablet) {
        lockAsync(OrientationLock.PORTRAIT_UP);
      }
    }
  });

  useFocusEffect(() => {
    if (message?.type === NotificationType.ScanCodeResult) {
      webViewRef.current?.postMessage({
        type: AppMessageType.ScanCodeSuccess,
        data: message.data,
      });
      clearNotification();
    }
  });

  useFocusEffect(() => {
    webViewRef.current?.postMessage({
      type: AppMessageType.EpubReady,
    });
  });

  /**
   * 点击了关闭按钮
   */
  const handleCloseButtonPressed = useCallback(() => {
    navigation.goBack();
  }, [navigation]);

  /**
   * 点击了返回到上一个网页按钮
   */
  const handleBackToPreviousWebPageButtonPressed = useCallback(() => {
    webViewRef.current?.goBack();
  }, []);

  // 新增：用于同步是否为初始页面
  const handleBackState = useCallback((canGoBack: boolean) => {
    setCapableBackToPreviousWebPage(canGoBack);
  }, []);

  /**
   * 渲染导航栏左侧按钮
   */
  const renderHeaderLeft = useCallback(
    (buttonProps: unknown) => {
      return (
        <JglXStack jglClassName='flex flex-row items-center justify-between'>
          <JglTouchable
            onPress={
              capableBackToPreviousWebPage
                ? handleBackToPreviousWebPageButtonPressed
                : handleCloseButtonPressed
            }
            jglClassName='h-[24px] w-[24px]'
            style={{ minHeight: 0, minWidth: 0 }}
          >
            <Image
              source={require('../assets/images/ic_back.png')}
              className='h-[24px] w-[24px]'
            />
          </JglTouchable>
          {capableBackToPreviousWebPage ? (
            <JglTouchable
              onPress={handleCloseButtonPressed}
              jglClassName='h-[24px] w-[24px]'
              mx={16}
              style={{ minHeight: 0, minWidth: 0 }}
            >
              <IconClose />
            </JglTouchable>
          ) : null}
        </JglXStack>
      );
    },
    [
      capableBackToPreviousWebPage,
      handleBackToPreviousWebPageButtonPressed,
      handleCloseButtonPressed,
    ],
  );

  const paddingBottom = useMemo(() => {
    return webviewNeedSafeBottom === 'true' ? safeAreaInsets.bottom : 0;
  }, [safeAreaInsets.bottom, webviewNeedSafeBottom]);

  /**
   * 处理是否是 App 环境请求
   */
  const handleAppEnvironmentRequest = useCallback(() => {
    webViewRef.current?.postMessage({
      type: AppMessageType.IsAppEnvironmentResponse,
      data: {
        isApp: isPlatform({
          runtime: 'rn',
        }),
      },
    });
  }, []);

  /**
   * 处理查询 SafeAreaInsets
   */
  const handleQuerySafeAreaInsets = useCallback(() => {
    webViewRef.current?.postMessage({
      type: AppMessageType.QuerySafeAreaInsetsResponse,
      data: {
        success: true,
        insets: safeAreaInsets,
      },
    });
  }, [safeAreaInsets]);

  /**
   * 处理跳转网页请求
   */
  const handlePushToWebPage = useCallback(
    (param: { title?: string; url: string }) => {
      bizRouter.push(JglRouterMap.webView, {
        url: appendQuery(param.url, { _t: Date.now() }),
        title: param.title,
      });
      webViewRef.current?.postMessage({
        type: AppMessageType.PushToWebPageResponse,
        data: {
          success: true,
        },
      });
    },
    [bizRouter],
  );

  /**
   * 处理震动
   */
  const handleVibrate = useCallback(
    (param: { feedback: HapticFeedbackTypes }) => {
      const { feedback } = param;
      hapticFeedback(feedback);
      webViewRef.current?.postMessage({
        type: AppMessageType.VibrateResponse,
        data: {
          success: true,
        },
      });
    },
    [],
  );

  /**
   * 处理咕噜球数量更新
   */
  const handleUpdateGuluBallCount = useCallback(
    (param: { guluBallBalance: number }) => {
      const { guluBallBalance } = param;
      setGuluBallCountAtom(guluBallBalance);
      webViewRef.current?.postMessage({
        type: AppMessageType.VibrateResponse,
        data: {
          success: true,
        },
      });
    },
    [setGuluBallCountAtom],
  );

  /**
   * 处理查询微信是否已安装请求
   */
  const handleQueryWeChatIsInstalled = useCallback(async () => {
    try {
      webViewRef.current?.postMessage({
        type: AppMessageType.QueryWeChatIsInstalledResponse,
        data: {
          success: true,
          weChatInstalled: !!isWeChatInstalled,
        },
      });
    } catch (error) {
      webViewRef.current?.postMessage({
        type: AppMessageType.QueryWeChatIsInstalledResponse,
        data: {
          success: false,
          weChatInstalled: false,
        },
      });
    }
  }, [isWeChatInstalled]);

  /**
   * 处理分享挑战结果到微信
   */
  const handleShareChallengeResultToWeChat = useCallback(
    (param: {
      shareWebPageUrl: string;
      miniProgramPagePath: string;
      title?: string;
      description?: string;
      coverUrl?: string;
    }) => {
      if (isWeChatInstalled) {
        const { shareWebPageUrl, miniProgramPagePath, coverUrl } = param;
        try {
          // shareWebpage({
          //   webpageUrl: shareWebPageUrl,
          //   ...rest,
          //   scene: NativeWechatConstants.WXSceneSession,
          // });
          shareMiniProgram({
            title: param.title,
            userName: envVars.miniProgramRawID(),
            path: miniProgramPagePath,
            // @ts-ignore
            miniProgramType: NativeWechatConstants.WXMiniProgramTypeRelease,
            webpageUrl: shareWebPageUrl,
            coverUrl: coverUrl ?? Icon.shareDetailCover1x,
            withShareTicket: false,
            // @ts-ignore
            scene: 0,
          });
        } catch (error) {
          // TODO: cenfeng - 分享失败？
        }
      }
    },
    [isWeChatInstalled],
  );

  const handleNavigateToVip = useCallback(() => {
    bizRouter.push(JglRouterMap.orderVip);
    webViewRef.current?.postMessage({
      type: AppMessageType.NavigateToVip,
    });
  }, [bizRouter]);

  const handleLaunchMiniProgram = useCallback(
    (param: { miniProgramPagePath: string; userName: string }) => {
      if (param) {
        const { miniProgramPagePath, userName } = param;
        try {
          launchMiniProgram(userName, miniProgramPagePath);
        } catch (error) {
          // TODO: 启动失败
        }
      }
    },
    [launchMiniProgram],
  );

  const handleH5Message = useCallback(
    (event: WebViewMessageEvent) => {
      if (event.nativeEvent.data === '') {
        return;
      }
      try {
        const message: H5ActionPayload = JSON.parse(event.nativeEvent.data);
        switch (message.type) {
          /** H5 - 退出页面 */
          case H5MessageType.Exit: {
            navigation.goBack();
            break;
          }
          /** H5 - 查询 SafeAreaInsets */
          case H5MessageType.QuerySafeAreaInsetsRequest: {
            handleQuerySafeAreaInsets();
            break;
          }
          /** H5 - 是否是 App 环境请求 */
          case H5MessageType.IsAppEnvironmentRequest: {
            handleAppEnvironmentRequest();
            break;
          }
          /** H5 - 跳转原生网页 */
          case H5MessageType.PushToWebPageRequest: {
            handlePushToWebPage(message.data);
            break;
          }
          /** H5 - 震动 */
          case H5MessageType.VibrateRequest: {
            handleVibrate(message.data);
            break;
          }
          /** H5 - 更新咕噜球余额 */
          case H5MessageType.UpdateGuluBallCountRequest: {
            handleUpdateGuluBallCount(message.data);
            break;
          }
          /** H5 - 查询微信是否安装 */
          case H5MessageType.QueryWeChatIsInstalledRequest: {
            handleQueryWeChatIsInstalled();
            break;
          }
          /** H5 - 分享挑战结果到微信 */
          case H5MessageType.ShareChallengeResultToWeChatRequest: {
            handleShareChallengeResultToWeChat(message.data);
            break;
          }
          /** H5 - 跳转会员页 */
          case H5MessageType.NavigateToVip: {
            handleNavigateToVip();
            break;
          }
          case H5MessageType.LaunchMiniProgramRequest: {
            handleLaunchMiniProgram(message.data);
            break;
          }
          case H5MessageType.UpdateH5Title: {
            if (message.data.title) {
              const newTitle = message.data.title;
              if (newTitle && newTitle.trim() !== '') {
                if (newTitle?.includes('.cn')) {
                  setTitle(titleFromRouter);
                } else {
                  setTitle(newTitle);
                }
              }
            }
            break;
          }
          case H5MessageType.OpenSystemBrowser: {
            Linking.openURL(message.data.url);
            break;
          }
          case H5MessageType.scanQRCodeRequest: {
            checkAndRequestPermission({
              permission: PermissionEnum.Camera,
              scene: PermissionPurposeScene.ScanCode,
            }).then((result) => {
              if (result) {
                // 跳转扫描二维码
                pushToScanSearchScreen();
              }
            });
            break;
          }
          case H5MessageType.pushNativeScreen: {
            // 跳转原生页面
            pushToNativeScreenByUrl(message.data.url);
            break;
          }
          default: {
            /* empty */
          }
        }
      } catch (error) {
        console.log('handleH5Message - error', error);
      }
    },
    [
      checkAndRequestPermission,
      handleAppEnvironmentRequest,
      handleLaunchMiniProgram,
      handleNavigateToVip,
      handlePushToWebPage,
      handleQuerySafeAreaInsets,
      handleQueryWeChatIsInstalled,
      handleShareChallengeResultToWeChat,
      handleUpdateGuluBallCount,
      handleVibrate,
      navigation,
      titleFromRouter,
    ],
  );

  const onHttpError = useCallback(
    (event: WebViewHttpErrorEvent) => {
      const errorUrl = event.nativeEvent.url;
      const newShowUseWechatScanTip =
        from === WebviewFrom.SCAN && isWeChatUrl(errorUrl);
      setShowUseWechatScanTip(newShowUseWechatScanTip);
    },
    [from],
  );

  const onRenderError = useCallback(() => {
    if (isWeChatUrl(url) && from === WebviewFrom.SCAN) {
      return (
        <JglYStack
          pos='absolute'
          style={StyleSheet.absoluteFill}
          alignItems='center'
          justifyContent='center'
          backgroundColor='white'
        >
          <JglView
            w={64}
            h={64}
            bg='#28C445'
            borderRadius={1000}
            jglClassName='flex-center'
          >
            <FontAwesome name='wechat' size={40} color='white' />
          </JglView>
          <JglText fontSize={14} mt={16} color='#8F8F8F'>
            此二维码不支持，去用微信扫码吧
          </JglText>
        </JglYStack>
      );
    }
    return <></>;
  }, [from, url]);

  /**
   * 处理WebView导航状态变化，获取网页标题
   */
  const handleNavigationStateChange = useCallback(
    (navState: WebViewNavigation) => {
      // 如果网页有标题且不为空，则更新标题
      if (navState.title && navState.title.trim() !== '') {
        if (navState.title?.includes('.cn')) {
          setTitle(titleFromRouter);
        } else {
          setTitle(navState.title);
        }
      }
      // 如果没有网页标题但有传入的标题，保持使用传入的标题
    },
    [titleFromRouter],
  );

  const parseCustomQueryItems = (url: string) => {
    const scheme = url.split('://')[0];
    if (scheme === 'http' || scheme === 'https') {
      if (url.indexOf('_appbiz') !== -1) {
        const splitArray = url.split('?');
        if (splitArray.length > 1) {
          const queryItems = splitArray?.[1]?.split('&');
          const parameters = {};
          queryItems?.forEach((itemString: string, index: number) => {
            const keyValue = itemString.split('=');
            parameters[keyValue[0]] = keyValue[1];
          });
          return parameters;
        }
      }
    }
    return null;
  };

  const onShouldStartLoadWithRequest = useCallback(
    (event: WebViewNavigation) => {
      const { url } = event;
      if (url.startsWith('http')) {
        if (url.includes('_appbiz=buy')) {
          const decodeUrl: string | null = decodeURIComponent(url);

          if (decodeUrl === null) {
            return true;
          }

          const parameters = parseCustomQueryItems(decodeUrl) as {
            bizId?: string;
            bizType?: string;
          };
          const { bizId, bizType } = parameters;
          router.push(routerMap.ConfirmOrder, {
            bizId,
            bizType,
          });
          setIsWaitBuy(true);
          return false;
        }
      }
      return true;
    },
    [],
  );

  useFocusEffect(
    useCallback(() => {
      if (isWaitBuy) {
        webViewRef.current?.reload();
        setIsWaitBuy(false);
      }
    }, [isWaitBuy]),
  );

  return (
    <>
      <Stack.Screen
        options={{
          // 动态获取网页本身的title，同时也支持传入
          headerTitle: title ?? '',
          headerLeft: renderHeaderLeft,
          headerShadowVisible: false,
          headerTitleAlign: 'center',
        }}
      />
      <StatusBar style='dark' />
      {showUseWechatScanTip ? (
        onRenderError()
      ) : (
        // 在 JGLWebView 组件中添加相关属性
        <JGLWebView<WebViewProps>
          ref={webViewRef}
          scrollEnabled={disableScroll !== 'true'}
          overScrollMode='never'
          onHttpError={onHttpError}
          containerStyle={{
            paddingBottom,
            backgroundColor: ' white',
          }}
          renderError={onRenderError}
          webviewDebuggingEnabled={featureToggles.webviewDebuggingEnabled()}
          onMessage={handleH5Message}
          // onShouldStartLoadWithRequest
          handleCapableBackToPreviousWebPage={handleBackState}
          source={{ uri: url ?? '' }}
          supportLogin={supportLogin === 'true'}
          vid={appVid ?? ''}
          onNavigationStateChange={handleNavigationStateChange}
          injectedJavaScript={injectedJavaScript}
          onShouldStartLoadWithRequest={onShouldStartLoadWithRequest}
        />
      )}
    </>
  );
}
