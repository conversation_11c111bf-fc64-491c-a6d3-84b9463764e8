import { useJglAiQAHistoryV2 } from '@jgl/ai-qa-v2';
import { eventBus } from '@jgl/ai-qa-v2/src/event/eventBus';
import {
  agreementStateAtom,
  lightVibrate,
  useNavigationBarHeight,
  useSafeAreaInsets,
} from '@jgl/biz-func';
import {
  JglGameButton,
  JglSpinner,
  JglStateView,
  JglText,
  JglTouchable,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import { useAtomValue } from 'jotai';
import { useCallback, useMemo, useRef } from 'react';
import {
  type NativeScrollEvent,
  type NativeSyntheticEvent,
  useWindowDimensions,
} from 'react-native';
import {
  ScrollView as GestureScrollView,
  RefreshControl,
} from 'react-native-gesture-handler';

const sideMenuWidthRatio = 0.85;
const onEndReachedThreshold = 30;

/**
 * Drawer 内容组件 - 用于 expo-router/drawer
 */
export const ChatSessionDrawerContent = () => {
  const safeInsets = useSafeAreaInsets();
  const windowSize = useWindowDimensions();
  const { width: SCREEN_WIDTH } = windowSize;

  const navigationBarHeight = useNavigationBarHeight();

  // 菜单宽度自适应屏幕
  const sideMenuWidth = useMemo(() => {
    return SCREEN_WIDTH * sideMenuWidthRatio;
  }, [SCREEN_WIDTH]);

  const {
    historyList,
    isLoading,
    isRefreshing,
    loadError,
    onRefresh,
    isLoadingMore,
    canLoadMore,
    onLoadMore,
  } = useJglAiQAHistoryV2();

  const agreementState = useAtomValue(agreementStateAtom);

  const scrollViewRef = useRef<GestureScrollView>(null);
  const hasReachedEnd = useRef(false); // 防止 onEndReached 重复触发

  const onEndReached = useCallback(() => {
    if (isLoadingMore || !canLoadMore) return;
    console.log('onEndReached');
    onLoadMore();
  }, [isLoadingMore, canLoadMore, onLoadMore]);

  const handleScroll = useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      const { contentOffset, layoutMeasurement, contentSize } =
        event.nativeEvent;
      const { height: contentHeight, width: _contentWidth } = contentSize;
      const { height: layoutHeight, width: _layoutWidth } = layoutMeasurement;
      const { y: offsetY, x: _offsetX } = contentOffset;

      let distanceToEnd: number;
      distanceToEnd = contentHeight - layoutHeight - offsetY;

      // 触底判断 (必须滚动过，且距离小于阈值，且之前未触底)
      const hasScrolled = offsetY > 0;
      if (
        hasScrolled &&
        distanceToEnd < onEndReachedThreshold &&
        !hasReachedEnd.current
      ) {
        console.log(
          `--handleScroll onEndReached current:${hasReachedEnd.current} distanceToEnd:${distanceToEnd} threshold:${onEndReachedThreshold}`,
        );
        hasReachedEnd.current = true; // 标记已触底
        onEndReached();
      } else if (distanceToEnd >= onEndReachedThreshold) {
        // 离开底部一段距离后，重置触底状态
        hasReachedEnd.current = false;
      }

      // 如果滚动到顶部/左边，重置触底状态
      if (offsetY <= 0) {
        hasReachedEnd.current = false;
      }
    },
    [onEndReached],
  );

  const renderRefreshControl = useMemo(() => {
    return <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />;
  }, [isRefreshing, onRefresh]);

  const renderHistoryList = useMemo(() => {
    return (
      <GestureScrollView
        ref={scrollViewRef}
        style={{ paddingHorizontal: 16 }}
        scrollEventThrottle={16}
        onScroll={handleScroll}
        refreshControl={renderRefreshControl}
      >
        {historyList.map((item, index) => {
          if (item.type === 'title') {
            return (
              <JglText
                mt={index !== 0 ? 16 : 0}
                mb={12}
                color='$color9'
                fontSize='$4'
                key={item.name}
              >
                {item.name}
              </JglText>
            );
          } else {
            return (
              <JglTouchable
                key={item.data.id}
                mb={12}
                justifyContent='flex-start'
                minHeight={24}
                onPress={() => {
                  lightVibrate();
                  eventBus.emit('onDrawerPressHistoryItem', {
                    sessionId: item.data.sessionId,
                  });
                }}
              >
                <JglText
                  color='$color12'
                  maxLines={1}
                  fontSize={16}
                  lineHeight={24}
                  key={item.data.id}
                >
                  {item.data.title}
                </JglText>
              </JglTouchable>
            );
          }
        })}
        {/* Loading Indicator */}
        {isLoadingMore && (
          <JglXStack
            p={15}
            justifyContent='center'
            alignItems='center'
            space={8}
          >
            <JglSpinner isLoading />
            {/* <JglSpinner size='small' />  Removed */}
            {/* <JglText color='$placeholderColor'>加载中...</JglText> */}
          </JglXStack>
        )}
      </GestureScrollView>
    );
  }, [handleScroll, historyList, isLoadingMore, renderRefreshControl]);

  const renderCreateNewChatSessionButton = useMemo(() => {
    return (
      <JglGameButton
        backgroundColor='white'
        secondaryBgColor='#7191FF'
        h={52}
        m={16}
        radius={10}
        onPress={() => {
          eventBus.emit('onDrawerPressCreateSession', {});
        }}
      >
        <JglXStack h={52} alignItems='center' justifyContent='center' space={4}>
          <JglText fontSize={16} fontWeight='500' color='#7191FF'>
            创建新对话
          </JglText>
        </JglXStack>
      </JglGameButton>
    );
  }, []);

  return (
    <JglYStack
      flex={1}
      pt={navigationBarHeight}
      pb={safeInsets.bottom + 16}
      backgroundColor='white'
      width={sideMenuWidth}
    >
      <JglStateView
        width={sideMenuWidth}
        isLoading={isLoading}
        isEmpty={agreementState !== 'agreed' ? true : historyList.length === 0}
        error={loadError}
        onRetry={onRefresh}
      >
        {renderHistoryList}
      </JglStateView>
      {renderCreateNewChatSessionButton}
    </JglYStack>
  );
};
