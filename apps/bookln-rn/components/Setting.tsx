import React from 'react';
import type { JSX } from 'react';
import {
  YTText,
  YTTouchable,
  YTView,
  YTXStack,
} from '@bookln/cross-platform-components';
import { useCallback } from 'react';
import {
  ActivityIndicator,
  Platform,
  SectionList,
  type SectionListData,
  Switch,
  View,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Image } from 'tamagui';
import {
  type Item,
  ItemType,
  type SectionData,
  useSetting,
} from '../hooks/useSetting';

/**
 * 设置
 */
export const Setting = () => {
  const {
    dataArray,
    onPressItem,
    isLoggingOut,
    messageNotificationSwitch,
    setMessageNotificationSwitch,
  } = useSetting();

  const keyExtractor = (item: Item) => `${item.type}`;

  const renderLogout = useCallback(
    (item: Item) => {
      return (
        <YTTouchable
          onPress={() => onPressItem(item.type)}
          paddingVertical={15}
          backgroundColor='white'
          alignItems='center'
          justifyContent='center'
          disabled={isLoggingOut}
        >
          <YTXStack
            flexDirection='row'
            alignItems='center'
            justifyContent='center'
          >
            {isLoggingOut && <ActivityIndicator color='#333333' />}
            <YTText fontSize={15} color='#333333'>
              {item.name}
            </YTText>
          </YTXStack>
        </YTTouchable>
      );
    },
    [isLoggingOut, onPressItem],
  );

  const renderMessageNotificationView = useCallback(
    (item: Item) => {
      if (Platform.OS === 'android') {
        return (
          <YTXStack
            flexDirection='row'
            alignItems='center'
            backgroundColor='white'
            paddingVertical={15}
            paddingHorizontal={20}
          >
            <YTText fontSize={14} color='#666666'>
              {isLoggingOut ? '正在退出' : item.name}
            </YTText>
            <View style={{ flex: 1 }} />
            <Switch
              value={messageNotificationSwitch}
              onValueChange={setMessageNotificationSwitch}
            />
          </YTXStack>
        );
      } else {
        return null;
      }
    },
    [isLoggingOut, messageNotificationSwitch, setMessageNotificationSwitch],
  );

  const renderItem = useCallback(
    ({ item }: { item: Item }): JSX.Element | null => {
      const disabled = item.disablePress || false;
      switch (item.type) {
        case ItemType.logout:
          return renderLogout(item);
        case ItemType.messageNotification:
          return renderMessageNotificationView(item);
        default:
          return (
            <YTTouchable
              onPress={() => onPressItem(item.type)}
              disabled={disabled}
              paddingVertical={15}
              paddingHorizontal={20}
              backgroundColor='white'
              alignItems='center'
              flexDirection='row'
            >
              <YTText fontSize={14} color='#666666'>
                {item.name}
              </YTText>
              <YTText
                flex={1}
                textAlign='right'
                fontSize={14}
                color='#333333'
                style={item.valueStyle}
              >
                {item.value}
              </YTText>
              {disabled ? (
                <View />
              ) : (
                <Image
                  className='ml-[13]'
                  source={require('../assets/images/ic_right_arrow.png')}
                />
              )}
            </YTTouchable>
          );
      }
    },
    [onPressItem, renderLogout, renderMessageNotificationView],
  );

  const renderSectionHeader = useCallback(
    (info: { section: SectionListData<Item, SectionData> }) => {
      const { data } = info.section;

      if (data.length === 0) {
        return null;
      } else {
        return <YTView height={10} />;
      }
    },
    [],
  );

  const { bottom } = useSafeAreaInsets();

  return (
    <YTXStack backgroundColor='#f6f7f8' height='100%'>
      <SectionList
        keyExtractor={keyExtractor}
        renderItem={renderItem}
        contentContainerStyle={{
          paddingBottom: bottom + 20,
        }}
        renderSectionHeader={renderSectionHeader}
        sections={dataArray}
      />
    </YTXStack>
  );
};
