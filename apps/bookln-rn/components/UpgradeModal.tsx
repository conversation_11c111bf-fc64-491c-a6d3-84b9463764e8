import { Jg<PERSON><PERSON><PERSON><PERSON>, JglText, JglXStack } from '@jgl/ui-v4';
import { LinearGradient } from 'expo-linear-gradient';
import { setStatusBarHidden } from 'expo-status-bar';
import { useCallback, useEffect, useMemo } from 'react';
import { Dimensions, ScrollView, View } from 'react-native';
import Modal from 'react-native-modal';
import * as Progress from 'react-native-progress';
import { Image } from 'tamagui';
import { useUpgradeModal } from '../hooks/useUpgradeModal';
import {
  STATUS_FAILED,
  STATUS_NOT_DOWNLOAD,
  STATUS_SUCCESSFUL,
} from '../upgrade/Const';

export const UpgradeModal = () => {
  const {
    isForceInstall,
    market,
    isShowUpgradeModal,
    appVersionDTO,
    downloadStatus,
    onPressGotoMarket,
    onPressClose,
    onUpdatePress,
  } = useUpgradeModal();

  const { name, content } = appVersionDTO ?? {};

  const isDownloading = useMemo(() => {
    const { status } = downloadStatus;
    return (
      status !== STATUS_NOT_DOWNLOAD &&
      status !== STATUS_SUCCESSFUL &&
      status !== STATUS_FAILED
    );
  }, [downloadStatus]);

  const versionLabel = useMemo(() => {
    const { status } = downloadStatus;
    switch (status) {
      case STATUS_NOT_DOWNLOAD:
        return '';
      case STATUS_SUCCESSFUL:
        return '已经下载完成';
      case STATUS_FAILED:
        return '下载出错';
      default:
        return '已更新 ';
    }
  }, [downloadStatus]);

  // const renderFooterView = useCallback(() => {
  //   const contentView =
  //     isForceInstall || market != null ? undefined : (
  //       <Text className={'text-text-secondary my-2 text-xs'}>
  //         关闭弹窗，后台将继续为您自动更新
  //       </Text>
  //     );
  //   return <View className={'h-[47px] items-center'}>{contentView}</View>;
  // }, [isForceInstall, market]);

  const renderButtonView = useCallback(() => {
    const { progress = 0, total = 0 } = downloadStatus;

    if (isDownloading && total > 0) {
      const percent = total > 0 ? progress / total : 0;
      return (
        <View className='mx-10 my-6 flex h-10 flex-row'>
          <Progress.Bar
            progress={percent}
            animated={false}
            borderWidth={0}
            width={null}
            height={40}
            color='#FFC80F'
            unfilledColor='#F1F1F3'
            borderRadius={20}
            className='flex-1'
          />

          <View className='absolute h-10 w-full items-center justify-center'>
            <JglText jglClassName='text-text text-xs'>
              {versionLabel}
              {(percent * 100).toFixed(0)}%
            </JglText>
          </View>
        </View>
      );
    }

    return (
      <JglXStack jglClassName='my-6 w-full flex-row px-6' space={8}>
        {isForceInstall ? null : (
          <JglButton
            color='white'
            style={{ borderColor: '#4E76FF' }}
            variant='outline'
            radius='circle'
            textStyle={{ color: '#4E76FF' }}
            jglClassName='flex-1'
            onPress={onPressClose}
          >
            暂不
          </JglButton>
        )}
        <JglButton
          jglClassName='flex-1'
          color='#4E76FF'
          radius='circle'
          textColor='white'
          onPress={onUpdatePress}
        >
          立即体验
        </JglButton>
      </JglXStack>
    );
  }, [
    downloadStatus,
    isDownloading,
    isForceInstall,
    onPressClose,
    onUpdatePress,
    versionLabel,
  ]);

  return (
    <>
      <Modal
        isVisible={isShowUpgradeModal}
        hasBackdrop
        coverScreen
        deviceHeight={Dimensions.get('screen').height + 1000}
        statusBarTranslucent
        hardwareAccelerated
        animationIn='fadeIn'
        animationOut='fadeOut'
        useNativeDriverForBackdrop
        useNativeDriver
      >
        <View className='flex items-center justify-center'>
          <View className='relative w-full max-w-xs rounded-2xl bg-white'>
            <LinearGradient
              colors={['#B3DEFF', '#FFFFFF']}
              className='absolute h-20 w-full rounded-t-2xl'
            />
            <Image
              source={require('../assets/images/upgrade/ic_upgrade_rocket.png')}
              className='absolute -top-6 right-[18px]'
              width={108}
              height={108}
            />
            <View className='mt-5'>
              <JglText jglClassName='text-text mx-10 mt-3 text-xl font-medium'>
                发现新版本
              </JglText>
              <JglText jglClassName='text-text-tertiary mx-10 mt-1 text-xs'>
                {name}
              </JglText>
              <JglText jglClassName='text-text mx-10 mt-[14px] text-sm font-medium'>
                更新内容:
              </JglText>
              <ScrollView className='max-h-32'>
                <JglText jglClassName='text-text mx-10 mt-3 text-[14px]'>
                  {content}
                </JglText>
              </ScrollView>
            </View>
            {renderButtonView()}
          </View>
        </View>
      </Modal>
    </>
  );
};
