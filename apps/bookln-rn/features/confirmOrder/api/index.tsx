import { type YTRequest } from '@yunti-private/net';

export type UserOrderServiceApiBuyDetailV2Params = {
  bizId?: string;
  bizType?: number;
};

export type Address = {
  city: string;
  district: string;
  gmtCreate: number;
  gmtModified: number;
  id: number;
  mobile: string;
  province: string;
  realName: string;
  street: string;
  userId: number;
  userName: string;
};

export type Coupon = {
  activeDate: number;
  activeType: number;
  amount: number;
  batchCode: string;
  batchId: number;
  cellValueForStatus: string;
  code: string;
  createUserId: number;
  createUserName: string;
  deductAmount: number;
  description: string;
  endTime: number;
  feeType: number;
  gmtCreate: number;
  gmtModified: number;
  id: number;
  name: string;
  scopeType: number;
  scopeVal: string;
  status: number;
  type: number;
  userId: number;
  userName: string;
  limitAmount?: number;
  startTime?: number;
};

export type UserOrderServiceApiBuyDetailV2Response = {
  address: Address;
  balance: number;
  canBalancePay: boolean;
  canThirdPay: boolean;
  coupons: Coupon[];
  needAddress: boolean;
  orderTypeText: string;
  price: number;
  puserId: number;
  thumbnails: string;
  title: string;
  userId: number;
  userName: string;
};

export const userOrderServiceApiBuyDetailV2 = (
  params: UserOrderServiceApiBuyDetailV2Params,
): YTRequest<UserOrderServiceApiBuyDetailV2Response> => {
  return {
    url: '/userorderservice/buydetail/v2.do',
    data: params,
    project: 'app',
  };
};

export type UserOrderServiceApiBuyV2Params = {
  bizId: string;
  bizType: number;
  couponId?: number;
  deductAmount?: number;
  srcChannel?: string;
  activeId?: number;
  activeType?: number;
};

export const userOrderServiceApiBuyV2 = (
  params: UserOrderServiceApiBuyV2Params,
): YTRequest<any> => {
  return {
    url: '/userorderservice/buy/v2.do',
    data: params,
    project: 'app',
  };
};

export type UserOrderServiceApiGetChargeUrlAndBuyV3Params = {
  bizId: string;
  bizType: number;
  chargeAmount: number;
  wxOpenId?: string;
  couponId?: number;
  deductAmount?: number;
  srcChannel?: string;
};

export const userOrderServiceApiGetChargeUrlAndBuyV3 = (
  params: UserOrderServiceApiGetChargeUrlAndBuyV3Params,
): YTRequest<any> => {
  return {
    url: '/userorderservice/getchargeurlandbuy/v3.do',
    project: 'app',
    data: {
      chargePayType: 2,
      wxTradeType: 'APP',
      ...params,
    },
  };
};

export const userOrderServiceApiIsChargePay = (params: {
  payOrderId: number;
}): YTRequest<any> => {
  return {
    url: '/userOrderService/isChargePay.do',
    project: 'app',
    data: params,
  };
};

export const userOrderServiceApiIsBuyV2 = (params: {
  id: string;
  type: number;
}): YTRequest<any> => {
  return {
    url: '/userorderservice/isbuy/v2.do',
    project: 'app',
    data: params,
  };
};
