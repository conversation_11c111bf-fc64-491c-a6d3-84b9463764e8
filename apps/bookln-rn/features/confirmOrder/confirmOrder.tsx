import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>on,
  YTCustomSheet,
  Y<PERSON>mage,
  YTScrollView,
  YTStateView,
  YTText,
  YTTouchable,
  YTView,
  YTXStack,
  YTYStack,
} from '@bookln/cross-platform-components';
import { IconStudyCoin } from '@bookln/icon-custom';
import { Check, ChevronRight, Circle, X } from '@bookln/icon-lucide';
import { BookCover, TextList } from '@jgl/biz-components';
import { useSafeAreaInsets } from '@jgl/biz-func';
import { fenToYuan, isIOS } from '@jgl/utils';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { useTheme } from 'tamagui';
import { AndroidPaymentMethod } from './androidPaymentMethod';
import { useBuyDetailInfo } from './hooks/useBuyDetailInfo';
import { useCoupons } from './hooks/useCoupons';
import { usePay } from './hooks/usePay';
import { IosRechargeCoin } from './iosRechargeCoin';
import { getOrderTypeImageSize, isBookCover } from './utils';
import { SafeAreaView } from 'react-native-safe-area-context';

export const ConfirmOrder = () => {
  const { data, bizType, bizId, totalPriceText, isLoading, error, retry } =
    useBuyDetailInfo();

  const { bottom } = useSafeAreaInsets();

  const theme = useTheme();

  const {
    isShowSelectCouponsModal,
    selectedCoupon,
    handleSelectCoupon,
    onPressShowCouponModal,
    onPressCloseCouponModal,
    getScopeAvailable,
    getTypeAvailable,
  } = useCoupons({
    data,
    bizId,
    bizType,
  });

  const { realPayment, selectedMethod, alertRef, onSelectedMethod, handlePay } =
    usePay({
      data,
      bizType,
      bizId,
      selectedCoupon,
    });

  const renderOrderPrice = useMemo(() => {
    if (isIOS()) {
      return (
        <YTXStack alignItems='center'>
          <IconStudyCoin width={24} height={24} />
          <YTText fontSize={14} fontWeight={'bold'} color={'$gray12'}>
            {totalPriceText}学币
          </YTText>
        </YTXStack>
      );
    } else {
      return (
        <YTXStack alignItems='center'>
          <YTText fontSize={14} fontWeight={'bold'} color={'$gray12'}>
            ¥ {totalPriceText}
          </YTText>
        </YTXStack>
      );
    }
  }, [totalPriceText]);

  const renderOrderInfo = useMemo(() => {
    const { width, height } = getOrderTypeImageSize(bizType);

    return (
      <YTXStack flex={1} overflow='hidden' p={12} gap={8}>
        {isBookCover(bizType) ? (
          <YTView w={78}>
            <BookCover
              thumbnails={data?.thumbnails}
              name={data?.title}
              showName={false}
              aspectRatio={width / height}
            />
          </YTView>
        ) : (
          <YTView>
            <YTImage
              borderRadius={4}
              source={data?.thumbnails}
              w={width}
              h={height}
            />
          </YTView>
        )}
        <YTYStack flex={1} gap={8} position='relative'>
          <YTText fontWeight={'bold'} fontSize={16} numberOfLines={2}>
            {data?.title}
          </YTText>
          <YTXStack>
            <YTView px={8} py={2} backgroundColor={'$gray3'} borderRadius={6}>
              <YTText color={'$gray11'} fontSize={12}>
                {data?.orderTypeText}
              </YTText>
            </YTView>
          </YTXStack>
          <YTView position='absolute' bottom={0} right={0}>
            {renderOrderPrice}
          </YTView>
        </YTYStack>
      </YTXStack>
    );
  }, [
    bizType,
    data?.orderTypeText,
    data?.thumbnails,
    data?.title,
    renderOrderPrice,
  ]);

  const renderNeedPayPrice = useMemo(() => {
    if (isIOS()) {
      return (
        <YTXStack alignItems='center'>
          <IconStudyCoin width={24} height={24} />
          <YTText fontSize={14} fontWeight={'bold'} color={'$orange9'}>
            {fenToYuan(realPayment)}学币
          </YTText>
        </YTXStack>
      );
    } else {
      return (
        <YTXStack alignItems='center'>
          <YTText fontSize={20} fontWeight={'bold'} color={'$orange9'}>
            ¥ {fenToYuan(realPayment)}
          </YTText>
        </YTXStack>
      );
    }
  }, [realPayment]);

  const hasCoupon = !!data?.coupons?.length;

  const renderCouponsText = useMemo(() => {
    if (selectedCoupon) {
      const amount = fenToYuan(selectedCoupon?.amount);
      return (
        <YTText fontSize={12} color={'$orange9'}>
          -{isIOS() ? `${amount}学币` : `¥ ${amount}`}
        </YTText>
      );
    } else if (hasCoupon) {
      return (
        <>
          <YTText color='$gray9' fontSize={12}>
            未选择优惠券
          </YTText>
          <ChevronRight color={theme.gray9.val} width={14} height={14} />
        </>
      );
    } else {
      return (
        <YTText color='$gray9' fontSize={12}>
          暂无可用优惠券
        </YTText>
      );
    }
  }, [hasCoupon, selectedCoupon, theme.gray9.val]);

  const renderBalance = useMemo(() => {
    if (isIOS()) {
      return null;
    }
    return (
      <YTXStack w={'$full'} p={12} borderTopWidth={1} borderTopColor={'$gray2'}>
        <YTXStack flex={1} w={'$full'} alignItems='center' gap={2}>
          <YTText color={'$gray12'} fontSize={12} fontWeight={'bold'}>
            可用余额
          </YTText>
          <YTText color={'$gray9'} fontSize={12}>
            (1学币 = 1元人民币)
          </YTText>
        </YTXStack>
        <YTText fontSize={12} color={'$orange9'}>
          {fenToYuan(data?.balance)}学币
        </YTText>
      </YTXStack>
    );
  }, [data?.balance]);

  return (
    <>
      <YTStateView
        isLoading={isLoading}
        onRetry={retry}
        error={error}
        w={'$full'}
        h={'$full'}
      >
        <SafeAreaView
          edges={['bottom']}
          className='flex h-full w-full flex-col'
        >
          <YTYStack flex={1} overflow='hidden'>
            <YTScrollView w={'$full'}>
              <YTYStack gap={12} backgroundColor={'$slBackground'}>
                <YTYStack w={'$full'} backgroundColor={'$background'}>
                  <YTYStack w={'$full'}>{renderOrderInfo}</YTYStack>
                  <YTTouchable
                    w={'$full'}
                    p={12}
                    borderTopWidth={1}
                    borderTopColor={'$gray2'}
                    disabled={!hasCoupon}
                    onPress={onPressShowCouponModal}
                  >
                    <YTView flex={1} justifyContent='center'>
                      <YTText
                        color={'$gray12'}
                        fontSize={12}
                        fontWeight={'bold'}
                      >
                        优惠券
                      </YTText>
                    </YTView>
                    <YTXStack alignItems='center'>{renderCouponsText}</YTXStack>
                  </YTTouchable>
                  {renderBalance}
                </YTYStack>
                <YTYStack
                  w={'$full'}
                  backgroundColor={'$background'}
                  minH={'$full'}
                  p={16}
                >
                  <YTText fontSize={16} fontWeight={'bold'}>
                    {isIOS() ? '充值学币' : '支付方式'}
                  </YTText>
                  {isIOS() ? (
                    <>
                      <IosRechargeCoin
                        data={data}
                        realPayment={realPayment}
                        onRechargeSuccess={retry}
                      />
                      <YTYStack py={12} gap={4}>
                        <YTText fontSize={16} fontWeight={'bold'}>
                          充值规则
                        </YTText>
                        <TextList
                          texts={[
                            '1学币=1元人民币',
                            '充值后的学币不会过期，但无法提现或转赠他人',
                            '学币为虚拟币，一旦充值不支持退款',
                            '学币可与优惠券一起使用',
                            '学币在购买课程时直接抵扣现金使用',
                          ]}
                          gap={4}
                          textsProps={{
                            fontSize: 12,
                            color: '$gray11',
                          }}
                        />
                      </YTYStack>
                    </>
                  ) : (
                    <AndroidPaymentMethod
                      selectedMethod={selectedMethod}
                      onSelectedMethod={onSelectedMethod}
                    />
                  )}
                </YTYStack>
              </YTYStack>
            </YTScrollView>
          </YTYStack>
          <YTYStack>
            <YTView
              px={16}
              py={2}
              w={'$full'}
              alignItems='center'
              justifyContent='center'
              backgroundColor={'$orange3'}
            >
              <YTText fontSize={12} color={'$orange9'}>
                该资源为虚拟产品，不支持退款
              </YTText>
            </YTView>
            <YTXStack px={16} py={12} gap={16}>
              <YTXStack flexShrink={0} gap={4} alignItems='center'>
                <YTText
                  flexShrink={0}
                  fontSize={16}
                  fontWeight={'bold'}
                  color={'$gray12'}
                >
                  应付:
                </YTText>
                {renderNeedPayPrice}
              </YTXStack>
              <YTTouchable
                onPress={handlePay}
                flex={1}
                backgroundColor={'$accent9'}
                h={48}
                borderRadius={12}
              >
                <YTText color={'#fff'} fontSize={16} fontWeight={'bold'}>
                  确认支付
                </YTText>
              </YTTouchable>
            </YTXStack>
          </YTYStack>
        </SafeAreaView>
      </YTStateView>
      <YTCustomSheet
        visible={isShowSelectCouponsModal}
        rightCloseButton={() => (
          <YTTouchable
            alignItems='center'
            justifyContent='center'
            p={3}
            mr={8}
            mt={8}
            onPress={onPressCloseCouponModal}
          >
            <X color={theme.gray9.val} width={18} height={18} />
          </YTTouchable>
        )}
        backdropOpacity={0.6}
        initialHeight={50}
        minHeight={50}
        maxHeight={50}
        title={
          <YTText fontSize={16} fontWeight={'bold'}>
            优惠券
          </YTText>
        }
        onPressCloseSheet={onPressCloseCouponModal}
      >
        <YTScrollView>
          <YTYStack gap={16} px={16} pb={bottom + 16}>
            {data?.coupons?.map((item) => {
              const isSelected = selectedCoupon?.id === item.id;

              const amount = fenToYuan(item?.amount);

              let validPeriod = '长期有效';
              if (item?.endTime) {
                if (item?.startTime) {
                  validPeriod = `${dayjs(item?.startTime).format(
                    'YYYY-MM-DD',
                  )}至${dayjs(item?.endTime).format('YYYY-MM-DD')}`;
                } else {
                  validPeriod = `至${dayjs(item?.endTime).format(
                    'YYYY-MM-DD',
                  )}`;
                }
              }

              const available =
                item?.activeType === 2 &&
                getScopeAvailable(item) &&
                getTypeAvailable(item);

              const onPress = () => {
                handleSelectCoupon(isSelected ? undefined : item);
                onPressCloseCouponModal();
              };

              return (
                <YTTouchable onPress={onPress} disabled={!available}>
                  <YTXStack>
                    <YTYStack
                      px={14}
                      py={12}
                      alignItems='center'
                      backgroundColor={available ? '$slRed2' : '$gray2'}
                      borderColor={available ? '$slRed5' : '$gray5'}
                      borderTopWidth={1}
                      borderLeftWidth={1}
                      borderBottomWidth={1}
                      borderRadius={8}
                    >
                      <YTXStack
                        alignItems='flex-end'
                        backgroundColor={'transparent'}
                      >
                        <YTText
                          color={available ? '$slRed9' : '$gray9'}
                          fontSize={22}
                          fontWeight={'bold'}
                        >
                          {amount}
                        </YTText>
                        <YTText
                          fontSize={12}
                          color={available ? '$slRed9' : '$gray9'}
                          fontWeight={'bold'}
                        >
                          {isIOS() ? '学币' : '元'}
                        </YTText>
                      </YTXStack>
                      <YTText
                        fontSize={12}
                        color={available ? '$slRed9' : '$gray9'}
                      >
                        {item?.limitAmount
                          ? `满${fenToYuan(item?.limitAmount)}元可用`
                          : '无使用门槛'}
                      </YTText>
                    </YTYStack>
                    <YTYStack
                      w={0}
                      borderLeftWidth={1}
                      my={4}
                      borderStyle='dashed'
                      borderColor={available ? '$slRed5' : '$gray5'}
                    />
                    <YTXStack
                      px={16}
                      flex={1}
                      py={12}
                      alignItems='center'
                      backgroundColor={available ? '$slRed2' : '$gray2'}
                      borderColor={available ? '$slRed5' : '$gray5'}
                      borderTopWidth={1}
                      borderRightWidth={1}
                      borderBottomWidth={1}
                      borderRadius={8}
                      gap={8}
                    >
                      <YTYStack backgroundColor={'transparent'} flex={1}>
                        <YTText
                          fontSize={14}
                          fontWeight={'bold'}
                          color={available ? '$gray12' : '$gray9'}
                        >
                          {item?.name ?? ''}
                        </YTText>
                        <YTText
                          numberOfLines={1}
                          fontSize={12}
                          color={available ? '$gray9' : '$gray8'}
                        >
                          {item?.description ?? ''}
                        </YTText>
                        <YTText
                          fontSize={12}
                          color={available ? '$slRed9' : '$gray9'}
                        >
                          {validPeriod}
                        </YTText>
                      </YTYStack>
                      <YTView backgroundColor={'transparent'}>
                        {isSelected ? (
                          <YTView
                            backgroundColor={'$slRed9'}
                            w={20}
                            h={20}
                            alignItems='center'
                            justifyContent='center'
                            borderRadius={10}
                          >
                            <Check
                              width={10}
                              height={10}
                              strokeWidth={4}
                              color={'white'}
                            />
                          </YTView>
                        ) : (
                          <Circle
                            width={20}
                            height={20}
                            color={
                              available ? theme.slRed5.val : theme.gray5.val
                            }
                          />
                        )}
                      </YTView>
                    </YTXStack>
                  </YTXStack>
                </YTTouchable>
              );
            })}
          </YTYStack>
        </YTScrollView>
      </YTCustomSheet>
      <YTAlert ref={alertRef} />
    </>
  );
};
