import { useCallback, useState } from 'react';
import type { UserOrderServiceApiBuyDetailV2Response } from '../api';
import { type Coupon } from '../api';
import type { UserOrderTypes } from '../types';

type Props = {
  data?: UserOrderServiceApiBuyDetailV2Response;
  bizId?: string;
  bizType?: UserOrderTypes;
};

export const useCoupons = (props: Props) => {
  const { data, bizId, bizType } = props;

  const [isShowSelectCouponsModal, setIsShowSelectCouponsModal] =
    useState<boolean>(false);

  const [selectedCoupon, setSelectedCoupon] = useState<Coupon | undefined>(
    undefined,
  );

  const handleSelectCoupon = useCallback((coupon?: Coupon) => {
    setSelectedCoupon(coupon);
  }, []);

  const onPressShowCouponModal = useCallback(() => {
    setIsShowSelectCouponsModal(true);
  }, []);

  const onPressCloseCouponModal = useCallback(() => {
    setIsShowSelectCouponsModal(false);
  }, []);

  const getScopeAvailable = useCallback(
    (coupon?: Coupon) => {
      if (coupon?.scopeType === 1) {
        return true;
      } else if (coupon?.scopeType === 2) {
        return coupon?.scopeVal?.toString() === data?.puserId?.toString();
      } else if (coupon?.scopeType === 3) {
        return coupon?.scopeVal?.toString() === bizId?.toString();
      }
    },
    [bizId, data?.puserId],
  );

  const getTypeAvailable = useCallback(
    (item: Coupon) => {
      if (item.type === 1) {
        return true;
      }
      return item.type?.toString() === bizType?.toString();
    },
    [bizType],
  );

  return {
    isShowSelectCouponsModal,
    selectedCoupon,
    handleSelectCoupon,
    onPressShowCouponModal,
    onPressCloseCouponModal,
    getScopeAvailable,
    getTypeAvailable,
  };
};
