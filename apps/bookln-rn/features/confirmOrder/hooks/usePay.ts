import { useWechat } from '@bookln/bookln-biz/src/login/hook/useWechat';
import { type YTAlertRef } from '@bookln/cross-platform-components';
import { container } from '@jgl/container';
import { isIOS, router } from '@jgl/utils';
import { useCallback, useMemo, useRef, useState } from 'react';
import { useToast } from 'react-native-toast-hybrid';
import {
  userOrderServiceApiBuyV2,
  userOrderServiceApiGetChargeUrlAndBuyV3,
  userOrderServiceApiIsBuyV2,
  userOrderServiceApiIsChargePay,
  type Coupon,
  type UserOrderServiceApiBuyDetailV2Response,
} from '../api';
import { type UserOrderTypes } from '../types';
import { useAppSelector } from '@jgl/biz-func';
import { useFocusEffect } from 'expo-router';

type Props = {
  data?: UserOrderServiceApiBuyDetailV2Response;
  selectedCoupon?: Coupon;
  bizType?: UserOrderTypes;
  bizId?: string;
};

export const usePay = (props: Props) => {
  const { data, selectedCoupon, bizType, bizId } = props;

  const { requestPayment } = useWechat();

  const alertRef = useRef<YTAlertRef>(null);

  const toast = useToast();

  const userInfo = useAppSelector((state) => state.userInfo);

  const isWechatBuy = useRef<boolean>(false);

  const [selectedMethod, setSelectedMethod] = useState(
    isIOS() ? undefined : 'wechat',
  );

  const realPayment = useMemo(() => {
    let price = data?.price ?? 0;
    price = price - (selectedCoupon?.amount ?? 0);
    if (!isIOS()) {
      price = price - (data?.balance ?? 0);
    }
    if (price < 0) {
      return 0;
    } else {
      return price;
    }
  }, [data?.balance, data?.price, selectedCoupon?.amount]);

  const onSelectedMethod = useCallback((id: string) => {
    setSelectedMethod(id);
  }, []);

  const deductAmount = useMemo(() => {
    if (selectedCoupon && data?.price) {
      if (selectedCoupon?.amount < data?.price) {
        return selectedCoupon?.amount;
      } else {
        return data?.price;
      }
    } else {
      return undefined;
    }
  }, [data?.price, selectedCoupon]);

  const handleBuyByCoins = useCallback(async () => {
    if (!bizId || !bizType) {
      return;
    }

    const response = await container.net().fetch(
      userOrderServiceApiBuyV2({
        bizId: bizId,
        bizType,
        couponId: selectedCoupon?.id,
        deductAmount,
      }),
    );
    if (response.success) {
      toast.done('购买成功', 1000);
      setTimeout(() => {
        router.back();
      }, 1000);
    } else {
      toast.error('购买失败');
    }
  }, [bizId, bizType, deductAmount, selectedCoupon?.id, toast]);

  const isBuy = useCallback(async () => {
    if (bizId && bizType && isWechatBuy.current) {
      const response = await container.net().fetch(
        userOrderServiceApiIsBuyV2({
          id: bizId,
          type: bizType,
        }),
      );
      if (response.data?.buySuccess) {
        isWechatBuy.current = false;
        toast.done('购买成功', 1000);
        setTimeout(() => {
          router.back();
        }, 1000);
      }
    }
  }, [bizId, bizType, toast]);

  useFocusEffect(() => isBuy);

  const handleBuyByWechatPay = useCallback(async () => {
    if (!bizId || !bizType) {
      return;
    }
    const response = await container.net().fetch(
      userOrderServiceApiGetChargeUrlAndBuyV3({
        bizId: bizId,
        bizType,
        chargeAmount: realPayment,
        couponId: selectedCoupon?.id,
        deductAmount: selectedCoupon?.amount,
        wxOpenId: userInfo?.clientId,
      }),
    );
    if (response.success) {
      try {
        requestPayment(response.data.params);
        isWechatBuy.current = true;
      } catch (error) {
        isWechatBuy.current = false;
        toast.error('购买失败');
      }
    }
  }, [
    bizId,
    bizType,
    realPayment,
    requestPayment,
    selectedCoupon?.amount,
    selectedCoupon?.id,
    toast,
    userInfo?.clientId,
  ]);

  const handlePay = useCallback(async () => {
    if (isIOS()) {
      if ((data?.balance ?? 0) >= realPayment) {
        alertRef.current?.show({
          title: '是否确定购买',
          onOk: () => {
            alertRef.current?.hide();
            handleBuyByCoins();
          },
          onCancel: () => {
            alertRef.current?.hide();
          },
        });
      } else {
        toast.error('余额不足');
      }
    } else {
      if (!realPayment) {
        alertRef.current?.show({
          title: '是否确定购买',
          onOk: () => {
            alertRef.current?.hide();
            handleBuyByCoins();
          },
          onCancel: () => {
            alertRef.current?.hide();
          },
        });
      } else {
        handleBuyByWechatPay();
      }
    }
  }, [
    data?.balance,
    handleBuyByCoins,
    handleBuyByWechatPay,
    realPayment,
    toast,
  ]);

  return { realPayment, selectedMethod, alertRef, handlePay, onSelectedMethod };
};
