import {
  agreementStateAtom,
  defaultPayPageParamsAtom,
  hasBeenOrderVIPPageAtom,
  isPlatform,
} from '@jgl/biz-func';
import { useAtomValue, useSetAtom } from 'jotai';
import { useCallback, useEffect, useRef } from 'react';
import type { EmitterSubscription } from 'react-native';
import { initConnection } from 'react-native-iap';
import { useToast } from 'react-native-toast-hybrid';

export const useInitIAP = () => {
  const agreementState = useAtomValue(agreementStateAtom);
  const initRef = useRef<boolean>(false);
  const purchaseUpdateSubscriptionRef = useRef<EmitterSubscription | null>(
    null,
  );
  const purchaseErrorSubscriptionRef = useRef<EmitterSubscription | null>(null);
  const setPayPageParamsAtom = useSetAtom(defaultPayPageParamsAtom);
  const hasBeenOrderVIPPage = useAtomValue(hasBeenOrderVIPPageAtom);
  const hasBeenOrderVIPPageRef = useRef<boolean>();

  useEffect(() => {
    hasBeenOrderVIPPageRef.current = hasBeenOrderVIPPage;
  }, [hasBeenOrderVIPPage]);

  const toast = useToast();

  // const chargeForApple = useCallback(
  //   async (param: { receipt: string; transactionId: string }) => {
  //     const { receipt, transactionId } = param;
  //     const request = UserOrderApi.chargeForApple({
  //       receipt,
  //       outTradeNo: transactionId,
  //     });
  //     const response = await container.net().fetch(request);
  //     console.log('🚀 ~ response:', response);
  //     if (response.success && response.data?.result === 'true') {
  //       return true;
  //     } else {
  //       return false;
  //     }
  //   },
  //   [],
  // );

  // const handlePurchaseUpdate = useCallback(
  //   async (purchase: SubscriptionPurchase | ProductPurchase) => {
  //     console.log(
  //       'purchaseUpdatedListener',
  //       purchase,
  //       hasBeenOrderVIPPageRef.current,
  //     );
  //     if (purchase?.transactionReceipt && purchase?.transactionId) {
  //       // TODO: cenfeng - 暂时直接盖个菊花，后面有时间用更合适的方式来交互
  //       if (!hasBeenOrderVIPPageRef.current) {
  //         toast.loading('正在恢复您的购买');
  //         reportEvent(weappReportEvent.restoreInAppPurchaseOrder, {
  //           time: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
  //         });
  //       } else {
  //         reportEvent(weappReportEvent.inAppPurchaseSuccess, {
  //           time: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
  //         });
  //       }
  //       const result = await chargeForApple({
  //         receipt: purchase?.transactionReceipt,
  //         transactionId: purchase?.transactionId,
  //       });
  //       console.log('🚀 ~ checkCurrentPurchase ~ result:', result);
  //       if (result) {
  //         try {
  //           const finishResult = await finishTransaction({
  //             purchase,
  //             isConsumable: true,
  //           });
  //           toast.hide();
  //           console.log('🚀 ~ finishResult:', finishResult);
  //           if (finishResult) {
  //             if (hasBeenOrderVIPPageRef.current) {
  //               router.push(routerMap.confirmSuccess);
  //               // TODO: cenfeng - 后面需要支持购买书？
  //               setPayPageParamsAtom({ orderType: OrderType.Vip });
  //             } else {
  //               toast.done('恢复购买成功');
  //             }
  //           }
  //         } catch (error) {
  //           toast.hide();
  //         }
  //       } else {
  //         toast.hide();
  //       }
  //     }
  //   },
  //   [chargeForApple, setPayPageParamsAtom, toast],
  // );

  // const handlePurchaseError = useCallback(
  //   (error: PurchaseError) => {
  //     console.warn('purchaseErrorListener', error);
  //     const isCancelledByUser =
  //       error != null &&
  //       error.code != null &&
  //       error.code === 'E_USER_CANCELLED';
  //     if (isCancelledByUser) {
  //       toast.hide();
  //     } else {
  //       toast.error(`[${error.code}]: ${error.message}`);
  //     }
  //   },
  //   [toast],
  // );

  const initIAP = useCallback(async () => {
    if (initRef.current) {
      return;
    }
    const initResult = await initConnection();
    if (initResult) {
      initRef.current = true;
      // purchaseUpdateSubscriptionRef.current = purchaseUpdatedListener(handlePurchaseUpdate);
      // purchaseErrorSubscriptionRef.current = purchaseErrorListener(handlePurchaseError);
    } else {
      console.log('useInitIAP 初始化失败');
    }
  }, []);

  useEffect(() => {
    // 只有 ios 使用内购，安卓不使用内购功能就不初始化
    if (
      agreementState === 'agreed' &&
      isPlatform({
        os: 'ios',
        runtime: 'rn',
      })
    ) {
      console.log('useInitIAP 开始初始化');
      initIAP();
    }
  }, [agreementState, initIAP]);

  useEffect(() => {
    return () => {
      purchaseErrorSubscriptionRef.current?.remove();
      purchaseErrorSubscriptionRef.current?.remove();
    };
  }, []);
};
