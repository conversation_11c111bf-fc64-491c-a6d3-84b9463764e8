import {
  agreementStateAtom,
  isLoadingUserInfoFromStorageAtom,
} from '@jgl/biz-func';
import { useNavigation } from 'expo-router';
import { useAtomValue } from 'jotai';
import { useEffect } from 'react';
import { atomMap } from '../atom';
import { useBooklnQuickAction } from './useBooklnQuickAction';
import { replaceToHome, replaceToLoginPage } from '../utils/routerHelper';

/**
 * 根据用户信息变化自动跳转到登录页、tab页
 */
export const useChangeRoot = () => {
  const isLoadingUserInfoFromStorage = useAtomValue(
    isLoadingUserInfoFromStorageAtom,
  );
  const agreementState = useAtomValue(agreementStateAtom);

  // 初始化快速操作
  const { handleDealQuickActionIfNeed, resetQuickActionIfExist } =
    useBooklnQuickAction();

  const isNetContainerInitialized = useAtomValue(
    atomMap.isNetContainerInitializedAtom,
  );

  // isLoadingUserInfoFromStorage
  // - undefined 读取本地用户信息未开始，应停留在index看菊花
  // - true 读取本地用户信息进行中，应停留在index看菊花
  const isUserLoaded = isLoadingUserInfoFromStorage === false;

  const navigation = useNavigation();

  useEffect(() => {
    if (isUserLoaded && isNetContainerInitialized) {
      switch (agreementState) {
        case 'agreed': {
          // 同意隐私协议进入首页
          // 延迟执行，避免跳转首页未完成
          replaceToHome();
          setTimeout(() => {
            handleDealQuickActionIfNeed();
          }, 1000);
          break;
        }
        case 'undetermined': {
          resetQuickActionIfExist();
          // 第一次安装APP没有用户信息或者取消了同意协议进入登录页面
          replaceToLoginPage();
          break;
        }
        case 'disagreed': {
          resetQuickActionIfExist();
          // 不同意进入浏览模式 进入首页
          replaceToHome();
          break;
        }
        default: {
          break;
        }
      }
    }
  }, [
    agreementState,
    handleDealQuickActionIfNeed,
    isNetContainerInitialized,
    isUserLoaded,
    navigation,
    resetQuickActionIfExist,
  ]);
};
