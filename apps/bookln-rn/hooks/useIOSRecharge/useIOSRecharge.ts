import { useApiQuery } from '@yunti-private/net-query-hooks';
import { useCallback, useEffect, useState } from 'react';
import {
  requestPurchase,
  useIAP,
  purchaseUpdatedListener,
  finishTransaction,
  type Purchase,
  PurchaseResult,
} from 'react-native-iap';
import { userOrderServiceApiChargeForApple } from './api';
import { container } from '@jgl/container';
import { useToast } from 'react-native-toast-hybrid';

const rechargeCoinArray = [
  {
    title: '1学币',
    value: 100,
    sku: 'sl.iap.1_coin',
  },
  {
    title: '6学币',
    value: 600,
    sku: 'sl.iap.6_coins',
  },
  {
    title: '18学币',
    value: 1800,
    sku: 'sl.iap.18_coins',
  },
  {
    title: '45学币',
    value: 4500,
    sku: 'sl.iap.45_coins',
  },
  {
    title: '68学币',
    value: 6800,
    sku: 'sl.iap.68_coins',
  },
  {
    title: '128学币',
    value: 12800,
    sku: 'sl.iap.128_coins',
  },
];

export const iapSkuArray = rechargeCoinArray.map((item) => item.sku);

export const useIOSRecharge = (
  onRechargeSuccess?: (amount?: number) => void,
) => {
  const { connected, initConnectionError, products, getProducts } = useIAP();
  const [isRecharging, setIsRecharging] = useState<boolean>(false);

  const toast = useToast();

  // 购买成功回调
  const onPurchaseSuccess = useCallback(
    async (purchase: Purchase) => {
      const { transactionId, transactionReceipt } = purchase;

      if (!transactionId || !transactionReceipt) {
        return;
      }
      const response = await container.net().fetch(
        userOrderServiceApiChargeForApple({
          receipt: transactionReceipt,
          outTradeNo: transactionId,
        }),
      );
      if (response.success) {
        toast.done('充值成功');
        onRechargeSuccess?.();
      } else {
        toast.error('充值失败');
      }
    },
    [onRechargeSuccess, toast],
  );

  // 监听购买更新
  useEffect(() => {
    const purchaseUpdateSubscription = purchaseUpdatedListener(
      async (purchase) => {
        if (purchase?.transactionReceipt && purchase?.transactionId) {
          // 验证购买
          try {
            // 这里应该调用您的服务器验证接口
            // const result = await verifyPurchase(purchase);

            // 完成交易
            await finishTransaction({
              purchase,
              isConsumable: true,
            });

            // 触发成功回调
            onPurchaseSuccess(purchase);
          } catch (error) {
            console.error('购买验证失败:', error);
            toast.error('充值失败');
            setIsRecharging(false);
          }
        }
      },
    );

    return () => {
      purchaseUpdateSubscription?.remove();
    };
  }, [onPurchaseSuccess, toast]);

  useEffect(() => {
    if (connected) {
      console.log('IAP 准备就绪');
      getProducts({
        skus: iapSkuArray,
      });
    }
  }, [connected, getProducts]);

  const handleBuyCoins = useCallback(
    async (targetSku?: string) => {
      if (targetSku && iapSkuArray.includes(targetSku)) {
        console.log('请求购买 - ', targetSku);
        setIsRecharging(true);
        toast.loading();
        try {
          await requestPurchase({
            sku: targetSku,
          });
        } catch (error) {
          toast.error('取消支付');
          setIsRecharging(false);
        }
      }
    },
    [toast],
  );

  return {
    rechargeCoinArray,
    handleBuyCoins,
    onPurchaseSuccess,
    isRecharging,
  };
};
