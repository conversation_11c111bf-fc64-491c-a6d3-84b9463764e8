import { storageKeys } from '@jgl/biz-func';
import { type BookInfoDTO } from '@jgl/biz-func/api/BookInfoDTO';
import { router, storage } from '@jgl/utils';
import { router as expoRouter } from 'expo-router';
import { InteractionManager } from 'react-native';
import { getRealMpHost } from './mpUrlHostHelper';
import { routerMap } from './routerMap';

/**
 * 替换到首页
 */
export const replaceToHome = () => {
  // // 重置首页tab状态栏样式
  // store.set(atomMap.homeBooklnTabStatusBarStyleAtom, 'dark');
  // // 重置首页tab状态栏样式
  // store.set(atomMap.homeMineTabStatusBarStyleAtom, 'dark');
  // 替换到首页
  expoRouter.dismissAll();
  InteractionManager.runAfterInteractions(() => {
    router.replace(routerMap.Home);
  });
};

/**
 * 替换到登录页面
 */
export const replaceToLoginPage = () => {
  expoRouter.dismissAll();
  InteractionManager.runAfterInteractions(() => {
    router.replace(routerMap.Login);
  });
  storage.setItem(storageKeys.agreementState, 'undetermined', { env: false });
};

/**
 * 跳转到图书详情
 * 绑定了智慧图书就跳转智慧图书
 * 未绑定智慧图书就跳转webview
 */
export const pushToBookDetail = (bookInfo: BookInfoDTO) => {
  const { id, idSign, hasBindAiBook } = bookInfo;
  if (hasBindAiBook === true) {
    router.push(routerMap.BookDetail, { bookId: id, idSign: idSign });
  } else {
    router.push(routerMap.WebView, {
      url: `${getRealMpHost()}/book.htm?id=${id}&sign=${idSign}`,
    });
  }
};

/**
 * 跳转到智慧图书学习页面
 */
export const pushToSmartBookStudy = (params: {
  bookId: number;
  bookIdSign: string;
  chapterId: number;
  pageId: number;
}) => {
  const { bookId, bookIdSign, chapterId, pageId } = params;
  router.replace(routerMap.BookStudy, {
    bookId,
    idSign: bookIdSign,
    defaultChapterId: chapterId,
    defaultPageId: pageId,
    isPreview: 'true',
  });
};

/**
 * 跳转到智慧图书预览
 */
export const pushToSmartBook = (params: {
  bookId: number;
  bookIdSign: string;
}) => {
  const { bookId, bookIdSign } = params;
  router.replace(routerMap.BookDetail, {
    bookId,
    idSign: bookIdSign,
    isPreview: 'true',
  });
};
