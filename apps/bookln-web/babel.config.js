module.exports = (api) => {
  api.cache(true);
  return {
    presets: [['babel-preset-expo', { unstable_transformImportMeta: true }]],
    plugins: [
      [
        '@tamagui/babel-plugin',
        {
          components: ['tamagui'],
          config: './tamagui.config.ts',
          logTimings: true,
          disableExtraction: process.env.NODE_ENV === 'development',
        },
      ],
      'jotai/babel/plugin-react-refresh',

      // https://www.nativewind.dev/quick-starts/expo#3-add-the-babel-plugin
      'nativewind/babel',

      // https://github.com/facebook/react-native/issues/36828#issuecomment-2020940725
      // https://github.com/jfilter/react-native-onboarding-swiper/issues/144#issuecomment-1679250385
      '@babel/plugin-transform-flow-strip-types',
      ['@babel/plugin-transform-private-methods', { loose: true }],

      // react-native-reanimated
      // // 必须是最后一个插件
      'react-native-reanimated/plugin',
    ],
    exclude: [],
  };
};
