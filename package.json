{"name": "bookln", "version": "1.0.0", "private": true, "description": "🐳书链，一套代码适配H5、小程序、React Native", "packageManager": "pnpm@8.15.9", "scripts": {"open:ci": "echo 打开打包任务链接 && open-cli http://ci.jump.yeteam.com:8080/view/%E4%B9%A6%E9%93%BE/", "open:figma": "echo 打开设计稿链接 && open-cli https://www.figma.com/design/FUYW21YXylVgfM7dokaXUY/%E4%B9%A6%E9%93%BEAPP?node-id=1503-3745&t=Vqtaf05i8MdD3nqG-1", "postinstall": "node ./scripts/postinstall.mjs", "pnpm:devPreinstall": "echo 在安装依赖前统一pnpm和node版本 && node scripts/preinstall.mjs", "fix:mismatches": "echo 保持所有依赖版本统一，修改为最新的一个 && pnpm syncpack fix-mismatches", "fix:node18:openssl": "cross-env NODE_OPTIONS=--openssl-legacy-provider", "r": "echo reset，移除node_modules并安装依赖 && (pnpx rimraf node_modules || true) && pnpm i", "rm": "echo \"提交merge request\" && node rm.js", "mr": "echo \"提交merge request，不会随机@处理人\" && yunti mr", "mrr": "echo \"提交merge request并删除自己分支，不会随机@处理人\" && yunti mr -r", "up:api": "echo 更新API SDK && pnpm update @yunti-private/api-* --latest -r", "expo:check": "echo 用Expo检查react-native依赖，防止版本不兼容的情况 && pnpm expo install --check -- -w", "h5:deploy": "echo 部署h5，仅CI执行 && pnpm run fix:node18:openssl pnpm yunti deploy-apps", "dev": "echo 开发书链rn && pnpm --filter @bookln-app/bookln-rn start", "dev:c": "echo 开发书链rn && pnpm --filter @bookln-app/bookln-rn start:clear", "dev:web": "echo 开发书链web && pnpm --filter @bookln-app/bookln-web web", "dev:web:c": "echo 开发书链web && pnpm --filter @bookln-app/bookln-web web:c", "bookln": "echo 开发书链web、rn，按w打开网页 && pnpm --filter @bookln-app/bookln-rn start", "bookln:c": "echo 开发书链web、rn（清除缓存），按w打开网页 && pnpm --filter @bookln-app/bookln-rn start:clear", "bookln:web": "echo 开发书链web && pnpm --filter @bookln-app/bookln-web web", "bookln:ios": "echo 开发书链iOS && (cd apps/bookln-rn && pnpm run ios)", "postbookln:pod": "sh ./scripts/postinstall.sh", "bookln:pod": "echo 安装书链iOS原生依赖 && (cd apps/bookln-rn && pnpm run pod)", "bookln:pod:update": "echo 更新书链iOS原生依赖 && (cd apps/bookln-rn && pnpm run pod:update)", "bookln:pod:new": "echo 安装书链iOS原生依赖 && (cd apps/bookln-rn-new-architecture && pnpm run pod)", "bookln:pod:c": "echo 清除并安装书链RN iOS原生依赖 && (cd apps/bookln-rn && pnpm run pod:c)", "bookln:pod:new:c": "echo 清除并安装书链RN iOS原生依赖 && (cd apps/bookln-rn-new-architecture && pnpm run pod:c)", "bookln:android": "echo 开发书链Android && pnpm --filter @bookln-app/bookln-rn android", "bookln:android:dep": "echo 查看书链Android依赖 && pnpm --filter @bookln-app/bookln-rn android:dep", "bookln:xcode": "echo 用Xcode打开书链iOS原生工程 && pnpm --filter @bookln-app/bookln-rn xcode", "bookln:xcode:new": "echo 用Xcode打开书链iOS原生工程 && pnpm --filter @bookln-app/bookln-rn-new-architecture xcode", "bookln:as": "echo 用Android Studio打开书链Android原生工程 && pnpm --filter @bookln-app/bookln-rn as", "bookln:dev:rn": "echo 开发书链RN，Windows、macOS可用 && pnpm --filter @bookln-app/bookln-rn start", "bookln:dev:rn:new": "echo 开发书链RN，Windows、macOS可用 && pnpm --filter @bookln-app/bookln-rn-new-architecture start", "bookln:dev:rn:c": "echo 开发书链RN，清除缓存，Windows、macOS可用 && pnpm --filter @bookln-app/bookln-rn start:clear", "bookln:dev:rn:new:c": "echo 开发书链RN，清除缓存，Windows、macOS可用 && pnpm --filter @bookln-app/bookln-rn-new-architecture start:clear", "bookln:dev:rn:r": "echo 开发书链RN，删除node_modules，清除缓存，Windows、macOS可用 && pnpm r && pnpm --filter @bookln-app/bookln-rn start:clear", "bookln:dev:ios": "echo 开发书链RN，编译iOS原生代码并启动iOS模拟器，macOS可用，需安装Xcode && (cd apps/bookln-rn && pnpm run ios)", "bookln:dev:ios:new": "echo 开发书链RN，编译iOS原生代码并启动iOS模拟器，macOS可用，需安装Xcode && (cd apps/bookln-rn-new-architecture && pnpm run ios)", "bookln:dev:ios:init": "echo 如果提示baseInfo.json找不到就可以执行一下 && (cd apps/bookln-rn && pnpm run ios:init)", "bookln:dev:android": "echo 开发书链RN，编译Android原生代码并启动Android真机或模拟器，Windows、macOS可用，需配置Android开发环境 && pnpm --filter @bookln-app/bookln-rn android", "bookln:dev:build": "echo 书链RN开发包 && pnpm bookln:dev:build:ios && pnpm bookln:dev:build:android", "bookln:dev:build:ios": "echo \"书链RN iOS开发包\" && pnpx open-cli http://ci.jump.yeteam.com:8080/view/%E9%B2%B8%E5%92%95%E5%99%9C/job/bookln-ios-dev/build?token=BOOKLN_DEV_CLIENT", "bookln:dev:build:android": "echo \"书链RN Android开发包\" && pnpx open-cli http://ci.jump.yeteam.com:8080/view/%E9%B2%B8%E5%92%95%E5%99%9C/job/bookln-android-dev/build?token=BOOKLN_DEV_CLIENT", "bookln:deploy:ci": "pnpm run --filter bookln deploy:ci", "bookln:build:h5": "pnpm run fix:node18:openssl pnpm --filter bookln build:h5", "prebookln:build:ios": "sh ./scripts/postinstall.sh", "bookln:build:ios": "echo \"书链RN iOS测试包\" && pnpm --filter @bookln-app/bookln-rn build:ios", "prebookln:build:ios:dev": "sh ./scripts/postinstall.sh", "bookln:build:ios:dev": "echo \"书链RN iOS开发包\" && pnpm --filter @bookln-app/bookln-rn build:ios:dev", "bookln:build:ios:upload": "echo \"上传书链RN iOS测试包到 TestFlight\" && pnpm --filter @bookln-app/bookln-rn build:ios:upload", "bookln:build:android": "echo \"书链RN Android测试包\" && pnpm --filter @bookln-app/bookln-rn build:android", "bookln:build:android:dev": "echo \"书链RN Android开发包\" && pnpm --filter @bookln-app/bookln-rn build:android:dev", "bookln:build:android:release": "echo \"书链RN Android渠道包\" && pnpm --filter @bookln-app/bookln-rn build:android:release", "bookln:build:android:channel": "echo \"通过加固APK打书链RN Android渠道\" && pnpm --filter @bookln-app/bookln-rn build:android:channel", "bookln:bundlejs:ios": "echo 使用 react native bundle 命令输出 ios 平台 js bundle 包 && pnpm --filter @bookln-app/bookln-rn bundlejs:ios", "bookln:bundlejs:android": "echo 使用 react native bundle 命令输出 android 平台 js bundle 包 && pnpm --filter @bookln-app/bookln-rn bundlejs:android", "syncpack:fix": "pnpm syncpack fix-mismatches", "check:unused:icon": "echo 检查 packages/icon/src/index.ts 中的是否有未用到 Icon && npx tsx scripts/analyze-unused-icons.ts", "check:unused:asIcon": "echo 检查 packages/icon/src/asIcon.ts 中的是否有未用到 asIcon && npx tsx scripts/analyze-unused-icons.ts -f packages/icon/src/asIcon.ts -p asIcon", "check:unused:arIcon": "echo 检查 packages/icon/src/arIcon.ts 中的是否有未用到 arIcon && npx tsx scripts/analyze-unused-icons.ts -f packages/icon/src/arIcon.ts -p arIcon", "check:unused:aiQAIcon": "echo 检查 packages/icon/src/aiQAIcon.ts 中的是否有未用到 aiQAIcon && npx tsx scripts/analyze-unused-icons.ts -f packages/icon/src/aiQAIcon.ts -p aiQAIcon", "check:unused:acIcon": "echo 检查 packages/icon/src/index.ts 中的是否有未用到的 AcIcon && npx tsx scripts/analyze-unused-icons.ts -f packages/icon/src/index.ts -p AcIcon", "update:im": "pnpm update @yunti-private/basic-im-impl @yunti-private/basic-im @yunti-private/basic-imds @yunti-private/connection @yunti-private/basic-im-dbutil-memo --latest -r", "knip:scan": "knip --config=\"./knip.json\"", "knip:fix": "knip --fix --allow-remove-files --config=\"./knip.json\""}, "devDependencies": {"@babel/cli": "7.23.0", "@babel/core": "7.27.4", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-transform-flow-strip-types": "7.26.5", "@babel/plugin-transform-optional-catch-binding": "7.24.1", "@babel/plugin-transform-optional-chaining": "7.23.4", "@babel/plugin-transform-private-methods": "7.25.9", "@babel/preset-env": "7.23.2", "@babel/preset-typescript": "7.24.1", "@bookln/bookln-biz": "workspace:*", "@ctrl/tinycolor": "4.0.2", "@expo/config": "11.0.10", "@larksuiteoapi/node-sdk": "1.22.0", "@nx/devkit": "20.4.2", "@nx/eslint-plugin": "20.4.2", "@pmmmwh/react-refresh-webpack-plugin": "0.5.10", "@react-native-community/cli": "19.1.0", "@tailwindcss/line-clamp": "0.4.4", "@tamagui/babel-plugin": "1.129.11", "@tamagui/metro-plugin": "1.129.11", "@tanstack/eslint-plugin-query": "5.68.0", "@testing-library/react": "15.0.7", "@types/ali-oss": "6.16.11", "@types/archiver": "6.0.2", "@types/crypto-js": "4.1.1", "@types/form-data": "2.5.0", "@types/howler": "2.2.9", "@types/jest": "29.5.12", "@types/lodash-es": "4.17.12", "@types/marked": "5.0.0", "@types/node": "18.15.6", "@types/qrcode": "1.5.5", "@types/react": "19.0.14", "@types/react-dom": "18.3.7", "@types/react-native-actionsheet": "2.4.3", "@types/react-native-canvas": "0.1.14", "@types/react-native-modalbox": "1.4.14", "@types/react-syntax-highlighter": "15.5.13", "@types/redux-logger": "3.0.9", "@types/rn-fetch-blob": "1.2.7", "@types/uuid": "3.4.0", "@types/webpack-bundle-analyzer": "4.7.0", "@types/webpack-env": "1.18.0", "@types/xml2js": "0.4.14", "@typescript-eslint/eslint-plugin": "6.21.0", "@typescript-eslint/parser": "6.21.0", "@vitest/coverage-v8": "3.0.5", "@yunti-private/antd-tailwind-presets": "1.0.8", "@yunti-private/cli": "0.1.24", "ali-oss": "6.18.1", "autoprefixer": "10.4.16", "babel-jest": "29.7.0", "babel-loader": "8.2.1", "babel-plugin-import": "1.13.6", "cache-loader": "4.1.0", "color-convert": "2.0.1", "cross-env": "7.0.3", "dingtalk-robot-sender": "1.2.0", "dotenv-flow": "4.1.0", "eslint": "8.57.1", "eslint-config-prettier": "9.1.0", "eslint-import-resolver-typescript": "4.4.4", "eslint-plugin-import": "2.29.0", "eslint-plugin-react": "7.33.2", "eslint-plugin-react-hooks": "4.6.0", "expo-atlas": "0.4.0", "fs-extra": "11.2.0", "git-lab-cli": "2.0.7", "identity-obj-proxy": "3.0.0", "install-peerdeps": "3.0.3", "jest": "29.7.0", "jest-expo": "53.0.7", "knip": "5.61.3", "less": "4.1.3", "less-loader": "11.1.0", "metro": "0.82.4", "miniprogram-ci": "2.1.9", "napi-postinstall": "0.3.0", "node-gyp-build": "4.6.0", "nx": "20.4.2", "open-cli": "7.2.0", "postcss": "8.4.23", "postcss-rem-to-responsive-pixel": "6.0.1", "prettier": "3.0.3", "prettier-plugin-tailwindcss": "0.5.6", "qrcode": "1.5.3", "react-native-calendars": "1.1305.0", "react-native-haptic-feedback": "2.3.3", "react-native-less-transformer": "1.4.0", "react-native-svg-transformer": "1.5.1", "react-query-external-sync": "2.2.3", "react-refresh": "0.11.0", "react-test-renderer": "18.2.0", "redux-logger": "3.0.6", "rimraf": "4.4.1", "socket.io-client": "4.8.1", "stylelint": "14.16.1", "syncpack": "12.3.3", "tailwind-merge": "2.1.0", "tailwindcss": "3.3.2", "thread-loader": "4.0.1", "translation-check": "1.0.3", "ts-babel": "6.1.7", "ts-jest": "29.1.2", "ts-node": "10.9.1", "tsconfig-paths-webpack-plugin": "4.0.1", "tsx": "4.19.0", "typescript": "5.8.3", "webpack": "5.69.0", "webpack-bundle-analyzer": "4.8.0", "xml2js": "0.6.2", "yargs": "17.7.2", "zx": "7.2.1"}, "dependencies": {"@antmjs/vantui": "3.2.1", "@babel/runtime": "7.21.0", "@bam.tech/react-native-image-resizer": "3.0.11", "@bookln/cross-platform-components": "workspace:*", "@bookln/icon-custom": "workspace:*", "@bookln/icon-lucide": "workspace:*", "@bookln/iconsax": "workspace:*", "@bookln/math-exam": "workspace:*", "@bookln/permission": "workspace:*", "@bookln/shared-pages": "workspace:*", "@config-plugins/react-native-blob-util": "6.0.0", "@expo/react-native-action-sheet": "4.1.1", "@gorhom/bottom-sheet": "5.1.6", "@jgl/ai-qa-v2": "workspace:*", "@jgl/biz-components": "workspace:*", "@jgl/biz-components-rojer-katex-mini": "workspace:*", "@jgl/biz-func": "workspace:*", "@jgl/biz-utils": "workspace:*", "@jgl/biz-utils-rn": "workspace:*", "@jgl/components": "workspace:*", "@jgl/container": "workspace:*", "@jgl/icon": "workspace:*", "@jgl/im": "workspace:*", "@jgl/logger": "workspace:*", "@jgl/ui-v4": "workspace:*", "@jgl/upload": "workspace:*", "@jgl/utils": "workspace:*", "@legendapp/list": "1.0.8", "@lodev09/react-native-exify": "0.2.7", "@lottiefiles/dotlottie-react": "0.14.2", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/hooks": "100.1.0", "@react-native-community/netinfo": "11.4.1", "@react-native-masked-view/masked-view": "0.3.2", "@react-native-voice/voice": "3.2.4", "@react-navigation/drawer": "7.4.1", "@react-navigation/native": "7.1.10", "@reduxjs/toolkit": "1.9.3", "@rneui/base": "4.0.0-rc.8", "@rneui/themed": "4.0.0-rc.8", "@rojer/katex-mini": "1.1.3", "@shopify/flash-list": "1.7.6", "@shopify/react-native-skia": "2.0.0-next.4", "@sleiv/react-native-graceful-exit": "0.1.0", "@tamagui/config": "1.129.11", "@tamagui/create-theme": "1.129.11", "@tamagui/next-theme": "1.129.11", "@tamagui/shorthands": "1.129.11", "@tamagui/themes": "1.129.11", "@tamagui/toast": "1.129.11", "@tanstack/query-async-storage-persister": "5.71.10", "@tanstack/react-query": "5.68.0", "@tanstack/react-query-persist-client": "5.71.10", "@yunti-private/api-aibook": "0.0.199", "@yunti-private/api-bizboot": "0.0.1023", "@yunti-private/api-booklnboot": "0.0.1016", "@yunti-private/api-gulu": "0.0.777", "@yunti-private/api-www": "0.0.1166", "@yunti-private/api-xingdeng-boot": "0.0.1230", "@yunti-private/basic-im": "1.0.58", "@yunti-private/basic-im-dbutil-memo": "1.0.31", "@yunti-private/basic-im-impl": "1.0.73", "@yunti-private/basic-imds": "1.0.25", "@yunti-private/connection": "1.2.10", "@yunti-private/env": "1.0.15", "@yunti-private/env-impl-rn": "1.0.1", "@yunti-private/env-impl-weapp": "1.0.4", "@yunti-private/env-impl-web": "1.0.18", "@yunti-private/logger-rn": "3.0.3", "@yunti-private/net": "1.0.27", "@yunti-private/net-impl-rn": "workspace:*", "@yunti-private/net-impl-web": "1.0.59", "@yunti-private/net-query-hooks": "workspace:*", "@yunti-private/net-sign-wasm": "0.1.14", "@yunti-private/rn-expo-updates-helper": "0.1.16", "@yunti-private/rn-iflytek": "0.1.6", "@yunti-private/rn-memory-logger": "1.0.6", "@yunti-private/utils-rn": "1.0.15", "@yunti-private/utils-universal": "1.0.15", "@yunti-private/utils-web": "1.0.15", "@yunti/react-native-chivox": "git+https://git.bookln.cn/github/react-native-chivox#main", "ahooks": "3.7.6", "archiver": "6.0.1", "axios": "1.5.1", "babel-plugin-lodash": "3.3.4", "burnt": "0.13.0", "classnames": "2.3.2", "copy-to-clipboard": "3.3.3", "crypto-js": "4.1.1", "dayjs": "1.11.7", "debug": "4.3.7", "deep-cleaner": "1.2.1", "dotenv": "16.3.1", "event-target-polyfill": "0.0.4", "expo": "53.0.20", "expo-audio": "0.4.8", "expo-blur": "14.1.5", "expo-build-properties": "0.14.8", "expo-camera": "16.1.11", "expo-clipboard": "7.1.5", "expo-constants": "17.1.7", "expo-dev-client": "5.2.4", "expo-device": "7.1.4", "expo-font": "13.3.2", "expo-haptics": "14.1.4", "expo-image": "2.4.0", "expo-image-manipulator": "13.1.7", "expo-image-picker": "16.1.4", "expo-linear-gradient": "14.1.5", "expo-linking": "7.1.7", "expo-localization": "16.1.6", "expo-quick-actions": "5.0.0", "expo-router": "5.1.4", "expo-screen-orientation": "8.1.7", "expo-splash-screen": "0.30.10", "expo-status-bar": "2.2.3", "expo-system-ui": "5.0.10", "expo-tracking-transparency": "5.2.4", "expo-updates": "0.28.14", "expo-video": "2.2.2", "expo-web-browser": "14.2.0", "form-data": "4.0.0", "howler": "2.2.4", "inobounce": "0.2.1", "inversify-props": "2.2.6", "jotai": "2.2.1", "js-base64": "3.7.5", "lodash-es": "4.17.21", "lottie-miniprogram": "1.0.12", "lottie-react-native": "7.2.2", "mime": "4.0.1", "mitt": "3.0.1", "moment": "2.29.4", "native-wechat": "git+https://git.bookln.cn/github/native-wechat#yunti/1.21.0/fix", "nativewind": "2.0.11", "query-string": "7.1.1", "react": "19.0.0", "react-dom": "19.0.0", "react-markdown-math": "1.0.2", "react-native": "0.79.5", "react-native-actionsheet": "git+https://git.bookln.cn/github/react-native-actionsheet#yunti", "react-native-anchor-point": "1.0.6", "react-native-animatable": "1.4.0", "react-native-audio-recorder-player": "3.6.14", "react-native-awesome-slider": "2.9.0", "react-native-blob-util": "0.19.4", "react-native-canvas": "0.1.40", "react-native-collapsible-tab-view": "6.2.1", "react-native-context-menu-view": "1.19.0", "react-native-device-info": "10.12.1", "react-native-drawer-layout": "3.3.0", "react-native-drop-shadow": "1.0.0", "react-native-gesture-handler": "2.24.0", "react-native-iap": "12.16.2", "react-native-image-colors": "2.5.0", "react-native-image-viewing": "0.2.2", "react-native-image-zoom-viewer": "3.0.1", "react-native-markdown-display": "7.0.2", "react-native-marked": "6.0.7", "react-native-math-view": "3.9.5", "react-native-modal": "14.0.0-rc.1", "react-native-modalbox": "2.0.2", "react-native-network-logger": "2.0.0", "react-native-pager-view": "6.7.1", "react-native-permissions": "5.4.1", "react-native-popover-view": "6.1.0", "react-native-qrcode-svg": "6.3.15", "react-native-reanimated": "3.17.5", "react-native-reanimated-table": "0.0.2", "react-native-redash": "18.1.3", "react-native-render-html": "6.3.4", "react-native-root-siblings": "5.0.1", "react-native-root-toast": "3.5.1", "react-native-safe-area-context": "5.4.0", "react-native-scalable-image": "1.1.0", "react-native-screens": "4.11.1", "react-native-select-dropdown": "3.4.0", "react-native-share": "12.0.11", "react-native-skia-shadow": "1.1.0", "react-native-svg": "15.11.2", "react-native-swiper": "1.6.0", "react-native-toast-hybrid": "2.6.0", "react-native-url-polyfill": "2.0.0", "react-native-user-agent": "git+https://git.bookln.cn/github/react-native-user-agent#master", "react-native-view-shot": "4.0.3", "react-native-vision-camera": "4.7.1", "react-native-web": "0.20.0", "react-native-webview": "13.15.0", "react-redux": "8.0.5", "react-syntax-highlighter": "15.6.1", "redux": "4.2.1", "reflect-metadata": "0.1.13", "rn-alioss": "0.2.5", "sonner": "2.0.6", "tamagui": "1.129.11", "use-debounce": "10.0.4", "uuid": "3.4.0", "wasm-dce": "1.0.2", "wasm-loader": "1.3.0", "weixin-js-sdk": "1.6.3", "weixin-js-sdk-ts": "1.6.1", "yet-another-abortcontroller-polyfill": "0.0.4", "zustand": "5.0.4"}, "pnpm": {"overrides": {"uuid": "3.4.0", "react-refresh": "0.14.0", "@yunti-private/env": "1.0.15", "@yunti-private/net-sign-wasm": "0.1.14", "core-js": "2.6.12", "markdown-it": "14.1.0", "metro": "0.82.4", "metro-cache": "0.82.4", "metro-config": "0.82.5", "metro-core": "0.82.4", "metro-runtime": "0.82.4", "metro-resolver": "0.82.4", "metro-babel-transformer": "0.82.4", "metro-source-map": "0.82.4", "metro-transform-worker": "0.82.5", "@react-navigation/drawer": "7.4.1", "@react-navigation/native": "7.1.10", "@react-navigation/bottom-tabs": "7.3.14", "@react-navigation/native-stack": "7.3.14", "babel-preset-expo": "13.2.0", "tamagui": "1.129.11", "@tamagui/colors": "1.129.11", "@tamagui/compose-refs": "1.129.11", "@tamagui/constants": "1.129.11", "@tamagui/create-theme": "1.129.11", "@tamagui/helpers": "1.129.11", "@tamagui/normalize-css-color": "1.129.11", "@tamagui/shorthands": "1.129.11", "@tamagui/simple-hash": "1.129.11", "@tamagui/theme-builder": "1.129.11", "@tamagui/themes": "1.129.11", "@tamagui/utils": "1.129.11", "@tamagui/types": "1.129.11", "@tamagui/use-did-finish-ssr": "1.129.11", "@tamagui/use-theme-name": "1.129.11", "@tamagui/use-force-update": "1.129.11", "@tamagui/web": "1.129.11", "marked": "5.0.5"}, "patchedDependencies": {"rn-alioss@0.2.5": "patches/<EMAIL>", "react-native-math-view@3.9.5": "patches/<EMAIL>", "react-native-canvas@0.1.40": "patches/<EMAIL>", "react-native-modalbox@2.0.2": "patches/<EMAIL>", "@sleiv/react-native-graceful-exit@0.1.0": "patches/@<EMAIL>", "react-native-audio-recorder-player@3.6.14": "patches/<EMAIL>"}}}