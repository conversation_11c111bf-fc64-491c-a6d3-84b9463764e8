import { useDeepCompareEffect } from 'ahooks';
import { useContext, useRef } from 'react';
import RootSiblingsManager from 'react-native-root-siblings';
import { FloatingAudioBallContext } from './context';
import { FloatingAudioBall } from './FloatingAudioBall';

export const useFloatingAudioBallContext = () => {
  return useContext(FloatingAudioBallContext);
};

export const useFloatingAudioBall = () => {
  const { isVisible, audioInfo, hide } = useFloatingAudioBallContext() ?? {};

  const siblingRef = useRef<RootSiblingsManager>(null);

  useDeepCompareEffect(() => {
    if (isVisible && !siblingRef.current) {
      // 创建一个新的 Sibling 实例
      const sibling = new RootSiblingsManager(
        <FloatingAudioBall audioInfo={audioInfo} onHide={hide} />,
      );
      siblingRef.current = sibling;
    } else if (!isVisible && siblingRef.current) {
      // 销毁 Sibling 实例
      siblingRef.current.destroy();
      siblingRef.current = null;
    } else if (isVisible && siblingRef.current) {
      // 如果已存在，则更新其内容
      siblingRef.current.update(
        <FloatingAudioBall audioInfo={audioInfo} onHide={hide} />,
      );
    }

    // 组件卸载时，确保销毁
    return () => {
      if (siblingRef.current) {
        siblingRef.current.destroy();
        siblingRef.current = null;
      }
    };
  }, [isVisible, audioInfo]);
};
