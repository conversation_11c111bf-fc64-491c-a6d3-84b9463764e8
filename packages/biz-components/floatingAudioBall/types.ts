import type { Dispatch, SetStateAction } from 'react';

export type FloatingAudioBallProps = {
  audioInfo: FloatingBallAudio | undefined;
  onHide?: () => void;
};

export type FloatingBallContextValue = {
  isVisible: boolean;
  audioInfo: FloatingBallAudio | undefined;

  show: (info?: FloatingBallAudio) => void;
  hide: () => void;
  setAudioInfo: Dispatch<SetStateAction<FloatingBallAudio | undefined>>;
};

export type FloatingBallAudio = {
  title: string;
};
