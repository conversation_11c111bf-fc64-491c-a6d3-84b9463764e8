import type { CoreType } from '@jgl/utils/src/hooks/useSIRecordTypes';
import type { HapticFeedbackTypes } from 'react-native-haptic-feedback';
import type { PermissionPurposeScene } from '@bookln/permission';
import type { KidsDetailUnitPass } from '../types/type';

export enum H5MessageType {
  /**
   * 退出页面
   */
  Exit = 'cn.bookln.jgl.h5.exit',
  /**
   * 查询 Safe Area Insets 请求
   */
  QuerySafeAreaInsetsRequest = 'cn.bookln.jgl.h5.querySafeAreaInsetsRequest',
  /**
   * 是否是 App 环境请求
   */
  IsAppEnvironmentRequest = 'cn.bookln.jgl.h5.isAppEnvironmentRequest',
  /**
   * 录音权限请求
   */
  RequestRecordAuthRequest = 'cn.bookln.jgl.h5.requestRecordAuthRequest',
  /**
   * 开始录音请求
   */
  StartRecordingRequest = 'cn.bookln.jgl.h5.startRecordingRequest',
  /**
   * 取消录音请求
   */
  CancelRecordingRequest = 'cn.bookln.jgl.h5.cancelRecordingRequest',
  /**
   * 结束录音请求
   */
  FinishRecordingRequest = 'cn.bookln.jgl.h5.finishRecordingRequest',
  /**
   * 请求设置少儿阅读游戏的记录
   */
  SetKidGameRecordRequest = 'cn.bookln.jgl.h5.setKidGameRecordRequest',
  /**
   * 保存少儿阅读游戏的记录
   */
  SaveKidGameRecordRequest = 'cn.bookln.jgl.h5.saveKidGameRecordRequest',
  /*
   * 跳转原生页面请求
   */
  PushToNativePageRequest = 'cn.bookln.jgl.h5.pushToNativePageRequest',
  /*
   * 跳转到网页请求
   */
  PushToWebPageRequest = 'cn.bookln.jgl.h5.pushToWebPageRequest',
  /**
   * 震动请求
   */
  VibrateRequest = 'cn.bookln.jgl.h5.vibrateRequest',
  /**
   * 保存图片到相册请求
   */
  SavePhotoToAlbumRequest = 'cn.bookln.jgl.h5.savePhotoToAlbumRequest',
  /**
   * 查询微信是否已安装请求
   */
  QueryWeChatIsInstalledRequest = 'cn.bookln.jgl.h5.queryWeChatIsInstalledRequest',
  /**
   * 分享图片到微信好友请求
   */
  ShareImageToWeChatFriendsRequest = 'cn.bookln.jgl.h5.shareImageToWeChatFriendsRequest',
  /**
   * 更新咕噜球余额请求
   */
  UpdateGuluBallCountRequest = 'cn.bookln.jgl.h5.updateGuluBallCountRequest',
  /**
   * 分享挑战结果到微信好友请求
   */
  ShareChallengeResultToWeChatRequest = 'cn.bookln.jgl.h5.shareChallengeResultToWeChatRequest',

  /* 跳转到会员页 */
  NavigateToVip = 'cn.bookln.jgl.h5.navigateToVip',

  /** 跳转小程序 */
  LaunchMiniProgramRequest = 'cn.bookln.jgl.h5.launchMiniProgramRequest',

  /** 更新 h5 的导航栏标题 */
  UpdateH5Title = 'cn.bookln.jgl.h5.updateH5Title',

  /** 跳转系统浏览器 */
  OpenSystemBrowser = 'cn.bookln.jgl.h5.openSystemBrowser',

  /** 跳转扫描二维码 */
  scanQRCodeRequest = 'cn.bookln.jgl.h5.scanQRCodeRequest',

  /**
   * 跳转原生页面
   */
  pushNativeScreen = 'cn.bookln.jgl.h5.pushNativeScreen',
}

export enum AppMessageType {
  /**
   * 是否是 App 环境响应
   */
  IsAppEnvironmentResponse = 'cn.bookln.jgl.h5.isAppEnvironmentResponse',
  /**
   * 查询 Safe Area Insets 响应
   */
  QuerySafeAreaInsetsResponse = 'cn.bookln.jgl.app.querySafeAreaInsetsResponse',
  /**
   * 录音权限响应
   */
  RequestRecordAuthResponse = 'cn.bookln.jgl.h5.requestRecordAuthResponse',
  /**
   * 开始录音响应
   */
  StartRecordingResponse = 'cn.bookln.jgl.app.startRecordingResponse',
  /**
   * 取消录音响应
   */
  CancelRecordingResponse = 'cn.bookln.jgl.h5.cancelRecordingResponse',
  /**
   * 结束录音响应
   */
  FinishRecordingResponse = 'cn.bookln.jgl.app.finishRecordingResponse',
  /**
   * 设置少儿阅读游戏的记录
   */
  SetKidGameRecordResponse = 'cn.bookln.jgl.h5.setKidGameRecordResponse',
  /**
   * 跳转原生页面响应
   */
  PushToNativePageResponse = 'cn.bookln.jgl.app.pushToNativePageResponse',
  /*
   * 跳转到网页响应
   */
  PushToWebPageResponse = 'cn.bookln.jgl.app.pushToWebPageResponse',
  /**
   * 震动响应
   */
  VibrateResponse = 'cn.bookln.jgl.app.vibrateResponse',
  /**
   * 保存海报响应
   */
  SavePhotoToAlbumResponse = 'cn.bookln.jgl.app.savePhotoToAlbumResponse',
  /**
   * 查询微信是否已安装响应
   */
  QueryWeChatIsInstalledResponse = 'cn.bookln.jgl.app.queryWeChatIsInstalledResponse',
  /**
   * 分享图片到微信好友响应
   */
  ShareImageToWeChatFriendsResponse = 'cn.bookln.jgl.app.shareImageToWeChatFriendsResponse',
  /**
   * 更新咕噜球余额响应
   */
  UpdateGuluBallCountResponse = 'cn.bookln.jgl.app.updateGuluBallCountResponse',
  /**
   * 分享挑战结果到微信好友响应
   */
  ShareChallengeResultToWeChatResponse = 'cn.bookln.jgl.app.shareChallengeResultToWeChatResponse',

  /* 跳转到会员页 */
  NavigateToVip = 'cn.bookln.jgl.app.navigateToVip',

  /**
   * 扫码成功
   */
  ScanCodeSuccess = 'cn.bookln.jgl.app.scanCodeSuccess',

  EpubReady = 'cn.bookln.epub.app.Ready',
}

export type AppAction = {
  type: AppMessageType;
  data?: object;
};

export type H5Action = {
  type: H5MessageType;
  data?: object;
};

export type StartRecordingRequestParam = {
  scene: PermissionPurposeScene;
  coreType?: CoreType;
  refText?: string;
};

export type AppActionPayload =
  | {
      type: AppMessageType.IsAppEnvironmentResponse;
      data: {
        isApp: boolean;
      };
    }
  | {
      type: AppMessageType.RequestRecordAuthResponse;
      data: {
        authResult: boolean;
      };
    }
  | {
      type: AppMessageType.StartRecordingResponse;
      data: {
        success: boolean;
      };
    }
  | {
      type: AppMessageType.FinishRecordingResponse;
      data: {
        content: string;
        success: boolean;
      };
    }
  | {
      type: AppMessageType.CancelRecordingResponse;
      data: {
        success: boolean;
      };
    }
  | {
      type: AppMessageType.SetKidGameRecordResponse;
      data: KidsDetailUnitPass[];
    }
  | {
      type: AppMessageType.PushToNativePageResponse;
      data: {
        success: boolean;
      };
    }
  | {
      type: AppMessageType.PushToWebPageResponse;
      data: {
        success: boolean;
      };
    }
  | {
      type: AppMessageType.VibrateResponse;
      data: {
        success: boolean;
      };
    }
  | {
      type: AppMessageType.SavePhotoToAlbumResponse;
      data: {
        success: boolean;
      };
    }
  | {
      type: AppMessageType.QueryWeChatIsInstalledResponse;
      data: {
        success: boolean;
        weChatInstalled: boolean;
      };
    }
  | {
      type: AppMessageType.QuerySafeAreaInsetsResponse;
      data: {
        success: boolean;
        insets: {
          top: number;
          right: number;
          bottom: number;
          left: number;
        };
      };
    }
  | {
      type: AppMessageType.ShareImageToWeChatFriendsResponse;
      data: {
        success: boolean;
      };
    }
  | {
      type: AppMessageType.UpdateGuluBallCountResponse;
      data: {
        success: boolean;
      };
    }
  | {
      type: AppMessageType.ShareChallengeResultToWeChatResponse;
      data: {
        success: boolean;
      };
    }
  | {
      type: AppMessageType.NavigateToVip;
    }
  | {
      type: AppMessageType.ScanCodeSuccess;
      data: string;
    }
  | {
      type: AppMessageType.EpubReady;
    };

export type H5ActionPayload =
  | {
      type: H5MessageType.IsAppEnvironmentRequest;
    }
  | {
      type: H5MessageType.RequestRecordAuthRequest;
      data: {
        scene: PermissionPurposeScene;
      };
    }
  | {
      type: H5MessageType.StartRecordingRequest;
      data: StartRecordingRequestParam;
    }
  | {
      type: H5MessageType.FinishRecordingRequest;
    }
  | {
      type: H5MessageType.CancelRecordingRequest;
    }
  | {
      type: H5MessageType.Exit;
    }
  | {
      type: H5MessageType.SetKidGameRecordRequest;
    }
  | {
      type: H5MessageType.SaveKidGameRecordRequest;
      data: KidsDetailUnitPass[];
    }
  | {
      type: H5MessageType.PushToNativePageRequest;
      data: {
        pagePath: string;
        pathParam?: Record<string, any>;
      };
    }
  | {
      type: H5MessageType.PushToWebPageRequest;
      data: {
        url: string;
        title?: string;
      };
    }
  | {
      type: H5MessageType.VibrateRequest;
      data: {
        feedback: HapticFeedbackTypes;
      };
    }
  | {
      type: H5MessageType.SavePhotoToAlbumRequest;
      data: {
        imageUrl: string;
        scene: PermissionPurposeScene;
      };
    }
  | {
      type: H5MessageType.QueryWeChatIsInstalledRequest;
    }
  | {
      type: H5MessageType.QuerySafeAreaInsetsRequest;
    }
  | {
      type: H5MessageType.ShareImageToWeChatFriendsRequest;
      data: {
        imageUrl: string;
      };
    }
  | {
      type: H5MessageType.UpdateGuluBallCountRequest;
      data: {
        guluBallBalance: number;
      };
    }
  | {
      type: H5MessageType.ShareChallengeResultToWeChatRequest;
      data: {
        shareWebPageUrl: string;
        miniProgramPagePath: string;
        title?: string;
        description?: string;
        coverUrl?: string;
      };
    }
  | {
      type: H5MessageType.NavigateToVip;
    }
  | {
      type: H5MessageType.LaunchMiniProgramRequest;
      data: {
        miniProgramPagePath: string;
        userName: string;
      };
    }
  | {
      type: H5MessageType.UpdateH5Title;
      data: {
        title: string;
      };
    }
  | {
      type: H5MessageType.OpenSystemBrowser;
      data: {
        url: string;
      };
    }
  | {
      type: H5MessageType.scanQRCodeRequest;
      data: {
        // needResult: 0 | 1; // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
        // scanType: ['qrCode', 'barCode']; // 可以指定扫二维码还是一维码，默认二者都有
      };
    }
  | {
      type: H5MessageType.pushNativeScreen;
      data: { url: string };
    };
