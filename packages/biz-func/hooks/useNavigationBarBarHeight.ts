import {
  getDefaultHeaderHeight,
  useHeaderHeight,
} from '@react-navigation/elements';
import type { UseNavigationBarBarHeight } from './useNavigationBarBarHeightTypes';
import { useWindowDimensions } from './useWindowDimensions';

export const useNavigationBarBarHeight: UseNavigationBarBarHeight = () => {
  const height = useHeaderHeight();
  const windowSize = useWindowDimensions();
  if (height === 0) {
    return getDefaultHeaderHeight(
      {
        width: windowSize.width,
        height: windowSize.height,
      },
      false,
      0,
    );
  }
  return height;
};
