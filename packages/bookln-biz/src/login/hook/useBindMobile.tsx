import { YTAlert, YTAlertActionType } from '@bookln/cross-platform-components';
import { AppLoginType } from '@jgl/biz-func';
import { JglText, JglTouchable } from '@jgl/ui-v4';
import { useRouterParams } from '@jgl/utils';
import { router } from 'expo-router';
import { useCallback, useMemo } from 'react';
import { ActivityIndicator } from 'react-native';
import { BindPhoneType } from '../type/BindPhone.type';
import type { PhoneAndSmsCodeViewBizProps } from '../type/PhoneAndSmsCodeView.type';
import { useLogin } from './useLogin';
import { useSupportSkipBind } from './useSupportSkipBind';
import type { WeChatInfo } from './useWechat';

export const useBindMobile = (): PhoneAndSmsCodeViewBizProps => {
  const {
    handleLoginAndBindMobile,
    handleBindMobile,
    isBinding,
    isSkippingBind,
    handleSkipBindAndLoginByWeChat,
  } = useLogin();
  const { weChatInfoStr, bindPhoneType = BindPhoneType.LoginBindPhone } =
    useRouterParams<{
      bindPhoneType: BindPhoneType;
      weChatInfoStr?: string;
    }>();
  const weChatInfo = weChatInfoStr
    ? (JSON.parse(weChatInfoStr) as WeChatInfo)
    : null;
  const supportSkipBind = useSupportSkipBind();

  const startLoginAndBindMobile = useCallback(
    async (args: {
      phoneNumber: string | undefined;
      smsCode: string | undefined;
    }) => {
      handleLoginAndBindMobile({
        mobile: args.phoneNumber ?? '',
        validCode: args.smsCode ?? '',
        ttpId: weChatInfo?.ttpId ?? '',
        nick: weChatInfo?.nick,
        sex: weChatInfo?.sex,
      });
    },
    [
      handleLoginAndBindMobile,
      weChatInfo?.nick,
      weChatInfo?.sex,
      weChatInfo?.ttpId,
    ],
  );

  /**
   * 只是绑定手机号
   */
  const startOnlyBindPhone = useCallback(
    async (args: {
      phoneNumber: string | undefined;
      smsCode: string | undefined;
    }) => {
      const result = await handleBindMobile({
        mobile: args.phoneNumber ?? '',
        validCode: args.smsCode ?? '',
      });

      if (result) {
        let message: string;
        if (bindPhoneType === BindPhoneType.ChangeBindPhone) {
          message = '更换手机号成功';
        } else {
          message = '绑定手机号成功';
        }

        // 使用alert明确告诉用户结果
        // 顺便也让键盘彻底消失，解决回到首页后UI异常问题
        // 相关bug：https://yuntikeji.feishu.cn/record/MnvArsNxPeaPHLcquF5ck028nIc
        YTAlert.show({
          title: message,
          actions: [
            {
              label: '确定',
              type: YTAlertActionType.主要按扭,
              onPress: () => {
                router.back();
                YTAlert.hide();
              },
            },
          ],
        });
      }
    },
    [bindPhoneType, handleBindMobile],
  );

  const handlePressBind = useCallback(
    async (args: {
      phoneNumber: string | undefined;
      smsCode: string | undefined;
    }) => {
      switch (bindPhoneType) {
        case BindPhoneType.LoginBindPhone: {
          startLoginAndBindMobile(args);
          break;
        }
        case BindPhoneType.ChangeBindPhone:
        case BindPhoneType.BindPhone: {
          startOnlyBindPhone(args);
          break;
        }
      }
    },
    [bindPhoneType, startLoginAndBindMobile, startOnlyBindPhone],
  );

  /**
   * 跳过绑定手机号，直接微信登录
   */
  const handleSkipBind = useCallback(async () => {
    await handleSkipBindAndLoginByWeChat({
      loginType: AppLoginType.WeChat,
      ttpId: weChatInfo?.ttpId ?? '',
      nick: weChatInfo?.nick,
      sex: weChatInfo?.sex,
      smallPhoto: weChatInfo?.smallPhoto,
    });
  }, [
    handleSkipBindAndLoginByWeChat,
    weChatInfo?.nick,
    weChatInfo?.sex,
    weChatInfo?.smallPhoto,
    weChatInfo?.ttpId,
  ]);

  const displayTitle = useMemo(() => {
    switch (bindPhoneType) {
      case BindPhoneType.LoginBindPhone:
      case BindPhoneType.BindPhone:
        return '绑定手机号';
      case BindPhoneType.ChangeBindPhone:
        return '更换手机号';
    }
  }, [bindPhoneType]);

  const placeholder = useMemo(() => {
    switch (bindPhoneType) {
      case BindPhoneType.LoginBindPhone:
      case BindPhoneType.BindPhone:
        return '请输入手机号';
      case BindPhoneType.ChangeBindPhone:
        return '请输入新手机号';
    }
  }, [bindPhoneType]);

  const description = useMemo(() => {
    if (bindPhoneType === BindPhoneType.ChangeBindPhone) {
      return '更换手机号后，请使用新手机号登录';
    }
  }, [bindPhoneType]);

  const headerRight = useCallback(() => {
    if (supportSkipBind && bindPhoneType === 'LoginBindPhone') {
      return (
        <JglTouchable
          jglClassName='flex-center'
          onPress={handleSkipBind}
          disabled={isSkippingBind}
        >
          {isSkippingBind ? (
            <ActivityIndicator size='small' color='#A0A0A0' />
          ) : (
            <JglText fontSize={14} color='#A0A0A0'>
              跳过
            </JglText>
          )}
        </JglTouchable>
      );
    }
    return null;
  }, [bindPhoneType, handleSkipBind, isSkippingBind, supportSkipBind]);

  return {
    confirmButtonTitle: displayTitle,
    isConfirmButtonLoading: isBinding,
    onPressConfirm: handlePressBind,
    showAgreementView: false,
    description,
    placeholder,
    headerRight,
  };
};
