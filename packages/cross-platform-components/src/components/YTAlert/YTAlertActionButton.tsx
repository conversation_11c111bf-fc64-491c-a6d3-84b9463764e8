import { hapticFeedback } from '@jgl/biz-utils-rn';
import { useCallback, useMemo, useState } from 'react';
import { HapticFeedbackTypes } from 'react-native-haptic-feedback';
import { But<PERSON>, Spinner, View } from 'tamagui';
import type { YTAlertActionButtonProps } from './YTAlertActionButton.type';
import { ACTION_TYPE_MAP } from './constants';

export const YTAlertActionButton = (props: YTAlertActionButtonProps) => {
  const { buttonProps, actionType, label, onPress } = props;

  const [loading, setLoading] = useState(false);

  const config = useMemo(() => {
    return ACTION_TYPE_MAP[actionType];
  }, [actionType]);

  const onPressButton = useCallback(async () => {
    hapticFeedback(HapticFeedbackTypes.impactLight);

    setLoading(true);
    await onPress();
    setLoading(false);
  }, [onPress]);

  return (
    <Button
      color={config.text}
      bg={config.background}
      borderColor={config.background}
      hoverStyle={{
        bg: config.backgroundHover,
        borderColor: config.borderColorHover,
      }}
      pressStyle={{
        bg: config.backgroundPress,
        borderColor: config.borderColorPress,
      }}
      borderRadius='$9'
      //   disabledStyle={{
      //     bg: '',
      //     borderColor: 'transparent',
      //   }}
      {...buttonProps}
      onPress={onPressButton}
    >
      <View
        animation='bouncy'
        flexDirection='row'
        alignItems='center'
        justifyContent='center'
        gap='$1.5'
      >
        {loading ? (
          <Button.Icon>
            <Spinner />
          </Button.Icon>
        ) : null}
        <Button.Text>{label}</Button.Text>
      </View>
    </Button>
  );
};
