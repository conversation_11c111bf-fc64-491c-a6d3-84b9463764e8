import type { AiChatApiListMsgDTO } from '@yunti-private/api-gulu';
import { atom } from 'jotai';
import type { JglAiQAMessage } from '../dto/JglAiQAMessage';
import { JglAiQAInputState } from '../enum/JglAiQAInputState';
import { JglAiQAVoiceState } from '../enum/JglAiQAVoiceState';

export const jglAiQAInputToolBarState = atom<JglAiQAInputState>(
  JglAiQAInputState.Voice,
);

export const jglAiQAVoiceState = atom<JglAiQAVoiceState>(
  JglAiQAVoiceState.Idle,
);

export const jglAiQAMessageListAtom = atom<JglAiQAMessage[]>([]);

export const jglAiQACurrentChatIdAtom = atom<number | undefined>(undefined);

export const jglAiQAInputToolBarVoiceRectAtom = atom<
  | {
      /** 节点的下边界坐标 */
      bottom: number;
      /** 节点的高度 */
      height: number;
      /** 节点的 ID */
      id: string;
      /** 节点的左边界坐标 */
      left: number;
      /** 节点的右边界坐标 */
      right: number;
      /** 节点的上边界坐标 */
      top: number;
      /** 节点的宽度 */
      width: number;
    }
  | undefined
>(undefined);

export const jglAiQAInputToolBarHeightAtom = atom<number>(0);

export const jglAiQAMessagePlayingAtom = atom<boolean>(false);

export const jglAiQACurrentRecordingMsgIdAtom = atom<string | undefined>(
  undefined,
);

export const jglAiQAChatMessageListHistoryMessagesAtom =
  atom<AiChatApiListMsgDTO>([]);

export const jglAiQAChatListNeedRefreshAtom = atom<boolean>(false);

export const jglAiQAChatMessageListAutoScrollAtom = atom<boolean>(false);

export const jglAiQAChatMessageListBackToBottomBtnVisibleAtom =
  atom<boolean>(false);

export const jglAiQAChatSessionListSideMenuVisibleAtom = atom<boolean>(false);

export const jglAiQAInputToolBarEditBarVisibleAtom = atom<boolean>(false);
