import ImageResizer from '@bam.tech/react-native-image-resizer';
import {
  PermissionEnum,
  PermissionHooks,
  PermissionPurposeScene,
} from '@bookln/permission';
import {
  agreementStateAtom,
  lightVibrate,
  useAgreementCheck,
} from '@jgl/biz-func';
import { container } from '@jgl/container';
import {
  JglImage,
  JglSpinner,
  JglText,
  JglTouchable,
  JglView,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import { OSSUploadNative } from '@jgl/upload/OSSUpload';
import { showToast, useKeyboardEvents, useWindowDimensions } from '@jgl/utils';
import { aiChatApiAudioTransText } from '@yunti-private/api-gulu';
import type { IImagePayload } from '@yunti-private/basic-im';
import { useAtom, useAtomValue, useSetAtom } from 'jotai';
import { RotateCw } from '@bookln/icon-lucide';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { type LayoutChangeEvent, Platform } from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import { KeyboardController } from 'react-native-keyboard-controller';
import { PERMISSIONS } from 'react-native-permissions';
import Animated, {
  Easing,
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { useDebouncedCallback } from 'use-debounce';
import { v4 as uuidv4 } from 'uuid';
import { imLoginStatusAtom } from '../atoms/IIMAtoms';
import {
  jglAiQACurrentRecordingMsgIdAtom,
  jglAiQAInputToolBarEditBarVisibleAtom,
  jglAiQAInputToolBarHeightAtom,
  jglAiQAInputToolBarState,
  jglAiQAMessageListAtom,
  jglAiQAVoiceState,
} from '../atoms/jglAiQAAtoms';
import type { JglAiQAMessage } from '../dto/JglAiQAMessage';
import { JglAiQAInputState } from '../enum/JglAiQAInputState';
import { JglAiQAMessageFlowType } from '../enum/JglAiQAMessageFlowType';
import { JglAiQAMessageSendingStatus } from '../enum/JglAiQAMessageSendingStatus';
import { JglAiQAMessageType } from '../enum/JglAiQAMessageType';
import { JglAiQAVoiceState } from '../enum/JglAiQAVoiceState';
import { eventBus } from '../event/eventBus';
import { useIMMessageListApiState } from '../hooks/useIMCoreLogic';
import { useRecordingAudio } from '../hooks/useRecordingAudio';
import { JglAiAQInputToolBarId } from '../utils/constants';
import {
  AutoHeightTextInput,
  type AutoHeightTextInputRef,
} from './AutoHeightTextInput';
import { useYTImagePreview } from '@bookln/cross-platform-components';

const MIN_HEIGHT = 4; // 最小高度 (PX)
const MAX_HEIGHT = 20; // 最大高度 (PX)
const THROTTLE_INTERVAL = 16; // UI 更新节流间隔 (ms)

type Props = {
  backgroundColor?: string;
  safeBottom: number;
  renderHeader?: React.ReactNode;
  onPressTakePhoto: () => void;
  onPressSend: (param: {
    textContent?: string;
    imagePayload?: IImagePayload;
  }) => void;
  onStartVoiceRecording?: () => void;
  onStopVoiceRecording?: () => void;
  feedBackModalVisible?: boolean;
};

// 定义手势事件类型
type GestureEvent = {
  x: number;
  y: number;
  absoluteX: number;
  absoluteY: number;
};

// 定义兼容的事件类型
type CompatibleTouchEvent = {
  changedTouches: Array<{
    clientX: number;
    clientY: number;
    pageX: number;
    pageY: number;
  }>;
};

type RecordingButtonRect = {
  x: number;
  y: number;
  width: number;
  height: number;
};

// 将手势事件转换为兼容的事件格式
const convertGestureToTouchEvent = (
  event: GestureEvent,
): CompatibleTouchEvent => {
  'worklet';
  return {
    changedTouches: [
      {
        clientX: event.x,
        clientY: event.y,
        pageX: event.absoluteX,
        pageY: event.absoluteY,
      },
    ],
  };
};

/**
 * <AUTHOR>
 * @description AI 问答输入工具栏
 * @date 2025_04_23
 */
export const JglAiQAInputToolBar = memo((props: Props) => {
  const {
    backgroundColor,
    safeBottom,
    renderHeader,
    onPressTakePhoto,
    onPressSend,
    onStartVoiceRecording,
    onStopVoiceRecording,
    feedBackModalVisible,
  } = props;
  const [inputState, setInputState] = useAtom(jglAiQAInputToolBarState);
  const [voiceState, setVoiceState] = useAtom(jglAiQAVoiceState);

  const [showEditMessageBar, setShowEditMessageBar] = useAtom(
    jglAiQAInputToolBarEditBarVisibleAtom,
  );
  const voiceStateRef = useRef(voiceState);
  useEffect(() => {
    if (voiceStateRef.current !== voiceState) {
      voiceStateRef.current = voiceState;
    }
  }, [voiceState]);

  const { showImagePreview, ImagePreviewerComponent } = useYTImagePreview();

  const { checkAndRequestPermission } = PermissionHooks.usePermission();

  const { withAgreementCheck } = useAgreementCheck();

  const { messageListApiState } = useIMMessageListApiState();

  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [uploadImageStatus, setUploadImageStatus] = useState<
    'idle' | 'uploading' | 'success' | 'failed'
  >('idle');
  const imageUploadParamRef = useRef<{
    url: string;
    width: number;
    height: number;
  } | null>(null);
  const imageUploadSourceRef = useRef<'photoLibrary' | 'camera' | undefined>(
    undefined,
  );
  const imageUploadingCanceledRef = useRef(false);

  useKeyboardEvents({
    willShow: ({ height }) => {
      setKeyboardHeight(height);
    },
    willHide: () => {
      setKeyboardHeight(0);
    },
  });

  // 使用单一 ref 管理录音状态机
  const recordingStateRef = useRef<{
    state: JglAiQAVoiceState;
    startTime?: number;
    clientY?: number;
    audioStarted: boolean;
  }>({
    state: JglAiQAVoiceState.Idle,
    audioStarted: false,
  });

  const inputRef = useRef<AutoHeightTextInputRef>(null);
  // 1. 新增一个 state 标记是否需要清空
  const shouldClearInputRef = useRef(false);

  // 添加录音流程锁
  const isRecordingFlowActiveRef = useRef(false);

  // 状态转换函数
  const transitionRecordingState = useCallback(
    (newState: JglAiQAVoiceState, additionalData = {}) => {
      const prevState = recordingStateRef.current.state;
      console.log(`录音状态转换: ${prevState} -> ${newState}`, additionalData);

      recordingStateRef.current = {
        ...recordingStateRef.current,
        state: newState,
        ...additionalData,
      };

      // 同步更新 UI 状态
      switch (newState) {
        case JglAiQAVoiceState.Idle:
          setVoiceState(JglAiQAVoiceState.Idle);
          break;
        case JglAiQAVoiceState.Recording:
          setVoiceState(JglAiQAVoiceState.Recording);
          break;
        case JglAiQAVoiceState.WaitForCancel:
          setVoiceState(JglAiQAVoiceState.WaitForCancel);
          break;
        case JglAiQAVoiceState.Error:
          setVoiceState(JglAiQAVoiceState.Error);
          break;
        case JglAiQAVoiceState.Canceled:
          setVoiceState(JglAiQAVoiceState.Canceled);
          break;
        case JglAiQAVoiceState.Finished:
          setVoiceState(JglAiQAVoiceState.Finished);
          break;
        case JglAiQAVoiceState.WaitingToStart:
          setVoiceState(JglAiQAVoiceState.WaitingToStart);
          break;
      }
    },
    [setVoiceState],
  );

  const [image, setImage] = useState<IImagePayload>();
  const [inputValue, setInputValue] = useState<string>();
  const [inputFocus, setInputFocus] = useState(false);

  useEffect(() => {
    if (!inputFocus) {
      KeyboardController.dismiss();
      inputRef.current?.blur();
    }
  }, [inputFocus]);

  const feedBackModalVisibleRef = useRef(feedBackModalVisible);
  const agreementState = useAtomValue(agreementStateAtom);
  useEffect(() => {
    if (feedBackModalVisible !== feedBackModalVisibleRef.current) {
      feedBackModalVisibleRef.current = feedBackModalVisible;
    }
    if (feedBackModalVisible) {
      setInputFocus(false);
    }
  }, [feedBackModalVisible]);

  const setJglAiQAInputToolBarHeight = useSetAtom(
    jglAiQAInputToolBarHeightAtom,
  );
  const setMessages = useSetAtom(jglAiQAMessageListAtom);
  const [currentRecordingMsgId, setCurrentRecordingMsgId] = useAtom(
    jglAiQACurrentRecordingMsgIdAtom,
  );
  const currentRecordingMsgIdRef = useRef(currentRecordingMsgId);
  useEffect(() => {
    if (currentRecordingMsgId !== currentRecordingMsgIdRef.current) {
      currentRecordingMsgIdRef.current = currentRecordingMsgId;
    }
  }, [currentRecordingMsgId]);

  const windowSize = useWindowDimensions();
  const voiceBarCounts = useMemo(() => {
    // 2 * x + (x - 1) * 4 = windowSize.width - 12 * 2 - 45 * 2
    //  2x + 4x - 4
    // 求 x
    const availableWidth = windowSize.width - 12 * 2 - 45 * 2;
    return Math.floor((availableWidth + 4) / 6);
  }, [windowSize.width]);

  const recordingCanceledRef = useRef(false);

  const handleRecordError = useCallback((errMsg?: string) => {
    console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - handleRecordError', errMsg);
    recordingCanceledRef.current = false;
  }, []);

  const handleSpeechToText = useCallback(
    async (url: string) => {
      const request = aiChatApiAudioTransText({
        url,
      });
      const response = await container.net().fetch(request);
      console.log(
        '🚀🚀🚀🚀🚀🚀 - leejunhui ~ handleSpeechToText - response:',
        response,
      );
      if (response.success) {
        if (response.data != null && response.data?.length > 0) {
          onPressSend({
            textContent: response.data,
            imagePayload: image,
          });
          setShowEditMessageBar(false);
          setInputValue('');
          setImage(undefined);
          setInputFocus(false); // 发送后重置焦点
        } else {
          showToast({
            title: '未识别到语音内容',
          });
          // 将「正在听取」消息干掉
          setMessages((prev) => {
            const newMessages = prev.filter(
              (msg) => msg.id !== currentRecordingMsgIdRef.current,
            );
            return newMessages;
          });
        }

        transitionRecordingState(JglAiQAVoiceState.Idle);
      } else {
        // TODO: leejunhui - 语音转文字失败 (2025_04_24)
        // 将「正在听取」消息干掉
        setMessages((prev) => {
          const newMessages = prev.filter(
            (msg) => msg.id !== currentRecordingMsgIdRef.current,
          );
          return newMessages;
        });
        showToast({
          title: '未识别到语音内容',
        });

        transitionRecordingState(JglAiQAVoiceState.Idle);
      }
    },
    [
      image,
      onPressSend,
      setImage,
      setMessages,
      setShowEditMessageBar,
      transitionRecordingState,
    ],
  );

  const handleRecordFinish = useCallback(
    async (tmpPath: string) => {
      console.log('handleRecordFinish', tmpPath);

      // 检查当前状态是否为已取消
      if (recordingStateRef.current.state === JglAiQAVoiceState.Canceled) {
        console.log('录音已取消，不处理录音结果');
        transitionRecordingState(JglAiQAVoiceState.Idle, {
          startTime: Date.now(),
        });
        return;
      }

      // 这里插入一条占位的消息，确保 UI 上有反馈
      const newTextMsgId = uuidv4();
      const newTextMsg: JglAiQAMessage = {
        id: newTextMsgId,
        flow: JglAiQAMessageFlowType.Out,
        contentType: JglAiQAMessageType.Text,
        sendingStatus: JglAiQAMessageSendingStatus.Sending,
        content: '正在听取...',
        groupCode: '',
      };
      setCurrentRecordingMsgId(newTextMsgId);

      setMessages((prev) => {
        return [...prev, newTextMsg];
      });

      try {
        // 上传音频
        const audioUrl = await OSSUploadNative.uploadToOSSPermanently({
          tmpPath,
          options: {
            net: container.net(),
          },
        });

        if (!audioUrl) {
          throw new Error('上传音频失败');
        }

        // 将语音转成文字
        await handleSpeechToText(audioUrl);
      } catch (error) {
        console.error('处理录音失败', error);

        // 移除占位消息
        setMessages((prev) => {
          return prev.filter((msg) => msg.id !== newTextMsgId);
        });

        showToast({
          title: '语音处理失败，请重试',
        });
        transitionRecordingState(JglAiQAVoiceState.Idle);
      }
    },
    [
      handleSpeechToText,
      setCurrentRecordingMsgId,
      setMessages,
      transitionRecordingState,
    ],
  );

  const {
    isRecording,
    startRecording: startRecordingAudio,
    stopRecording: stopRecordingAudio,
  } = useRecordingAudio({
    duration: 60 * 1000,
    onRecordingFinish: handleRecordFinish,
    onRecordingError: handleRecordError,
  });

  const isFingerStillPressedRef = useRef(false);

  const handleLongPress = useCallback(
    async (event: GestureEvent) => {
      console.log(
        'leejunhui - 🔥🔥🔥🔥🔥🔥 - handleLongPress',
        event,
        'Finger pressed at start:',
        isFingerStillPressedRef.current,
      );

      if (messageListApiState === 'loading') {
        showToast({
          title: '初始化数据中，请稍候',
        });
        return;
      }
      if (messageListApiState === 'error') {
        eventBus.emit('reloadMessageList', {});
        return;
      }

      // 检查录音流程锁
      if (isRecordingFlowActiveRef.current) {
        console.log('录音流程正在进行中，忽略本次触发');
        return;
      }

      // 使用节流的 Haptic 反馈
      lightVibrate();

      const compatibleEvent = convertGestureToTouchEvent(event);
      const { changedTouches } = compatibleEvent;

      if (changedTouches.length > 0) {
        try {
          isRecordingFlowActiveRef.current = true;

          const authResult = await checkAndRequestPermission({
            scene: PermissionPurposeScene.AiQA,
            permission: PermissionEnum.Microphone,
            secondPermissions:
              Platform.OS === 'android'
                ? [
                    PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE,
                    PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE,
                  ]
                : undefined,
          });

          if (!isFingerStillPressedRef.current) {
            console.log('用户在权限请求期间或之前已松手，不启动录音');
            isRecordingFlowActiveRef.current = false;
            return;
          }

          if (!authResult) {
            console.log('权限未授予，不启动录音');
            transitionRecordingState(JglAiQAVoiceState.Idle);
            isRecordingFlowActiveRef.current = false;
            return;
          }

          console.log('权限已授予，手指仍按下，准备开始录音流程');
          transitionRecordingState(JglAiQAVoiceState.WaitingToStart, {
            startTime: Date.now(),
            clientY: changedTouches[0]?.clientY,
          });

          const result = await startRecordingAudio();

          if (result) {
            if (
              isFingerStillPressedRef.current &&
              recordingStateRef.current.state ===
                JglAiQAVoiceState.WaitingToStart
            ) {
              console.log('录音启动成功，转换为 Recording 状态');
              transitionRecordingState(JglAiQAVoiceState.Recording, {
                audioStarted: true,
              });
              onStartVoiceRecording?.();
            } else {
              console.log(
                '启动录音后发现手指已松开或状态已改变，停止录音并清理',
                {
                  isPressed: isFingerStillPressedRef.current,
                  state: recordingStateRef.current.state,
                },
              );
              await stopRecordingAudio();
              onStopVoiceRecording?.();
              if (
                recordingStateRef.current.state ===
                  JglAiQAVoiceState.WaitingToStart ||
                recordingStateRef.current.state === JglAiQAVoiceState.Recording
              ) {
                transitionRecordingState(JglAiQAVoiceState.Idle);
              }
              isRecordingFlowActiveRef.current = false;
            }
          } else {
            console.log('录音启动失败，转换为 Error 状态');
            transitionRecordingState(JglAiQAVoiceState.Error);
            onStopVoiceRecording?.();
            isRecordingFlowActiveRef.current = false;
          }
        } catch (error) {
          console.error('录音流程发生异常:', error);
          transitionRecordingState(JglAiQAVoiceState.Error);
          onStopVoiceRecording?.();
          isRecordingFlowActiveRef.current = false;
        }
      }
    },
    [
      checkAndRequestPermission,
      messageListApiState,
      onStartVoiceRecording,
      onStopVoiceRecording,
      startRecordingAudio,
      stopRecordingAudio,
      transitionRecordingState,
    ],
  );

  const handleTouchCancel = useCallback(
    (e: CompatibleTouchEvent) => {
      console.log('handleTouchCancel', e);

      // 检查录音流程锁
      if (!isRecordingFlowActiveRef.current) {
        console.log('录音流程未激活，忽略取消操作');
        return;
      }

      // 先切换 UI 状态为 Canceled
      transitionRecordingState(JglAiQAVoiceState.Canceled);
      stopRecordingAudio().then(() => {
        onStopVoiceRecording?.();
        recordingCanceledRef.current = true;
        // stopRecording 完成后再释放锁并切换 Idle
        isRecordingFlowActiveRef.current = false;
        transitionRecordingState(JglAiQAVoiceState.Idle);
      });
    },
    [onStopVoiceRecording, stopRecordingAudio, transitionRecordingState],
  );

  const handleTouchEnd = useCallback(
    (e: CompatibleTouchEvent) => {
      console.log('handleTouchEnd', e);

      // 检查录音流程锁
      if (!isRecordingFlowActiveRef.current) {
        console.log('录音流程未激活，忽略结束操作');
        return;
      }

      const { changedTouches } = e;
      const currentState = recordingStateRef.current.state;

      if (currentState === JglAiQAVoiceState.WaitingToStart) {
        console.log(
          'handleTouchEnd - 录音在 WaitingToStart 状态时松手，转为 Idle',
        );
        // 直接释放锁并切换 Idle
        isRecordingFlowActiveRef.current = false;
        transitionRecordingState(JglAiQAVoiceState.Idle);
        return;
      }

      if (
        currentState !== JglAiQAVoiceState.Recording &&
        currentState !== JglAiQAVoiceState.WaitForCancel
      ) {
        console.log(
          'handleTouchEnd - 不在 Recording 或 WaitForCancel 状态，直接返回. Current state:',
          currentState,
        );
        isRecordingFlowActiveRef.current = false;
        transitionRecordingState(JglAiQAVoiceState.Idle);
        return;
      }

      if (changedTouches.length > 0) {
        const { pageY: clientY } = changedTouches[0] || {};
        const cancelRecording = (clientY || 0) < 0;

        console.log('handleTouchEnd - 触摸结束', {
          clientY,
          toolBarTop: 0,
          cancelRecording,
          currentState,
        });

        if (cancelRecording) {
          console.log('handleTouchEnd - 取消录音 (上移)');
          transitionRecordingState(JglAiQAVoiceState.Canceled);
          stopRecordingAudio().then(() => {
            onStopVoiceRecording?.();
            recordingCanceledRef.current = true;
            isRecordingFlowActiveRef.current = false;
            transitionRecordingState(JglAiQAVoiceState.Idle);
          });
        } else {
          console.log('handleTouchEnd - 完成录音');
          if (
            currentState === JglAiQAVoiceState.Recording ||
            currentState === JglAiQAVoiceState.WaitForCancel
          ) {
            transitionRecordingState(JglAiQAVoiceState.Finished);
            stopRecordingAudio().then(() => {
              onStopVoiceRecording?.();
              isRecordingFlowActiveRef.current = false;
              transitionRecordingState(JglAiQAVoiceState.Idle);
            });
          } else {
            // isRecordingFlowActiveRef.current = false;
            // transitionRecordingState(JglAiQAVoiceState.Idle);
            stopRecordingAudio().then(() => {
              onStopVoiceRecording?.();
              isRecordingFlowActiveRef.current = false;
              transitionRecordingState(JglAiQAVoiceState.Idle);
            });
          }
        }
      } else {
        // isRecordingFlowActiveRef.current = false;
        // transitionRecordingState(JglAiQAVoiceState.Idle);
        stopRecordingAudio().then(() => {
          onStopVoiceRecording?.();
          isRecordingFlowActiveRef.current = false;
          transitionRecordingState(JglAiQAVoiceState.Idle);
        });
      }
    },
    [onStopVoiceRecording, stopRecordingAudio, transitionRecordingState],
  );

  const debouncedHandleTouchEnd = useDebouncedCallback(
    (e: CompatibleTouchEvent) => {
      handleTouchEnd(e);
    },
    300,
    { leading: true, trailing: false },
  );

  const onPressDeletePhoto = useCallback(
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    (e: any) => {
      e.stopPropagation();
      e.preventDefault();
      console.log('onPressDeletePhoto');
      setImage(undefined);
    },
    [setImage],
  );

  const onPressSwitchInputState = useCallback(() => {
    if (messageListApiState === 'loading') {
      showToast({
        title: '初始化数据中，请稍候',
      });
      return;
    }
    if (messageListApiState === 'error') {
      eventBus.emit('reloadMessageList', {});
      return;
    }

    if (voiceState === JglAiQAVoiceState.Finished) {
      return;
    }

    lightVibrate();

    if (inputState === JglAiQAInputState.Voice) {
      console.log(
        'leejunhui - 🔥🔥🔥🔥🔥🔥 - onPressSwitchInputState - 切换到键盘',
      );
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
    setInputState(
      inputState === JglAiQAInputState.Voice
        ? JglAiQAInputState.Keyboard
        : JglAiQAInputState.Voice,
    );
  }, [inputState, messageListApiState, setInputState, voiceState]);

  const handleSendPress = useCallback(() => {
    if (messageListApiState === 'loading') {
      showToast({
        title: '初始化数据中，请稍候',
      });
      return;
    }
    if (messageListApiState === 'error') {
      eventBus.emit('reloadMessageList', {});
      return;
    }
    console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - handleSendPress');

    shouldClearInputRef.current = true;

    const trimmedInputValue = inputValue?.trim?.() || '';

    if (trimmedInputValue.length === 0 && !image) {
      setInputValue('');
      setInputFocus(false); // 发送后重置焦点
      inputRef.current?.blur();
      return;
    }

    onPressSend({
      textContent: trimmedInputValue,
      imagePayload: image,
    });
    setShowEditMessageBar(false);
    setInputValue('');
    setImage(undefined);
    setInputFocus(false); // 发送后重置焦点
    inputRef.current?.blur();
  }, [
    messageListApiState,
    inputValue,
    image,
    onPressSend,
    setShowEditMessageBar,
    setImage,
  ]); // 确保依赖项完整

  const renderSendButtonComponent = useMemo(
    () => (
      <JglTouchable
        w={26}
        h={26}
        ai='center'
        jc='center'
        onPress={handleSendPress}
        bg={'#4E76FF'}
        borderRadius={9999}
      >
        <JglImage
          source={require('../assets/images/ic_ai_chat_send.png')}
          w={14}
          h={14}
        />
      </JglTouchable>
    ),
    [handleSendPress], // 依赖 handleSendPress
  );

  const renderSwitchInputButtonComponent = useMemo(
    () => (
      <JglTouchable
        w={26}
        h={26}
        ai='center'
        jc='center'
        onPress={withAgreementCheck(onPressSwitchInputState)}
      >
        <JglImage
          source={
            inputState === JglAiQAInputState.Voice
              ? require('../assets/images/ic_ai_chat_switch_to_keyboard.png')
              : require('../assets/images/ic_ai_chat_switch_to_voice.png')
          }
          w={26}
          h={26}
        />
      </JglTouchable>
    ),
    [withAgreementCheck, onPressSwitchInputState, inputState], // 依赖 onPressSwitchInputState 和 inputState
  );

  // 通用的图片上传处理函数
  const handleImageUpload = useCallback(
    async (
      event: { url: string; width: number; height: number },
      source?: 'photoLibrary' | 'camera',
    ) => {
      imageUploadParamRef.current = event;
      imageUploadSourceRef.current = source;
      console.log(
        `leejunhui - 🔥🔥🔥🔥🔥🔥 - handleImageUpload from ${source}`,
        event,
      );
      const { url, width, height } = event;
      setUploadImageStatus('uploading');

      // 上传前对图片进行压缩
      const maxWidth = 1920;
      const maxHeight = 1920;

      try {
        const compressedResponse = await ImageResizer.createResizedImage(
          url,
          maxWidth,
          maxHeight,
          'JPEG',
          50,
        );
        console.log(
          '🚀 ~ JglAiQAInputToolBar ~ compressedResponse:',
          compressedResponse,
        );
        if (compressedResponse.uri) {
          const imageUrl = await OSSUploadNative.uploadToOSSPermanently({
            tmpPath: compressedResponse.uri,
            options: {
              net: container.net(),
            },
            bizCode: 'bookln',
            imageDimensions: {
              width: compressedResponse.width,
              height: compressedResponse.height,
            },
          });
          if (imageUploadingCanceledRef.current) {
            imageUploadingCanceledRef.current = false;
            return;
          }
          if (imageUrl) {
            setUploadImageStatus('success');
            setImage({
              url: imageUrl,
              size: 0,
              width,
              height,
            });
            imageUploadParamRef.current = null;
            imageUploadSourceRef.current = undefined;
          } else {
            setUploadImageStatus('failed');
          }
        } else {
          setUploadImageStatus('failed');
          showToast({
            title: '图片压缩失败',
          });
        }
      } catch (error) {
        console.log('JglAiQAInputToolBar - handleImageUpload - error', error);
        setUploadImageStatus('failed');
        showToast({
          title: '图片上传失败',
        });
      }
    },
    [setImage],
  );

  const onPressPreviewWaitForSendImage = useCallback(() => {
    console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - onPressPreviewWaitForSendImage');

    if (image) {
      // Taro.previewImage({
      //   urls: [image],
      // });
      showImagePreview([image.url]);
    }
  }, [image, showImagePreview]);

  const handleUploadImageRetry = useCallback(() => {
    if (imageUploadParamRef.current) {
      handleImageUpload(
        imageUploadParamRef.current,
        imageUploadSourceRef.current,
      );
    }
  }, [handleImageUpload]);

  const renderImage = useMemo(() => {
    if (uploadImageStatus === 'uploading') {
      return (
        <JglTouchable
          onPress={onPressPreviewWaitForSendImage}
          w={80}
          h={80}
          ai='center'
          jc='center'
          borderRadius={8}
          bg='$color4'
          position='relative'
        >
          <JglSpinner isLoading={true} color='#4E76FF' size={21} />
          <JglTouchable
            position='absolute'
            right={0}
            top={0}
            minW={22}
            minH={22}
            ai='center'
            jc='center'
            borderRadius={9999}
            onPress={() => {
              imageUploadingCanceledRef.current = true;
              setUploadImageStatus('idle');
            }}
            p={2}
          >
            <JglView
              w={18}
              h={18}
              ai='center'
              jc='center'
              borderRadius={9999}
              backgroundColor='#0000004D'
              p={5}
            >
              <JglImage
                source={require('../assets/images/ic_ai_chat_delete_wait_send_image.png')}
                w={8}
                h={8}
              />
            </JglView>
          </JglTouchable>
        </JglTouchable>
      );
    } else if (uploadImageStatus === 'failed') {
      return (
        <JglTouchable
          onPress={onPressPreviewWaitForSendImage}
          w={80}
          h={80}
          ai='center'
          jc='center'
          borderRadius={8}
          position='relative'
        >
          <JglTouchable
            bg='white'
            width={32}
            height={32}
            borderRadius={100}
            ai='center'
            jc='center'
            onPress={handleUploadImageRetry}
          >
            <RotateCw size={20} color='#171717' />
          </JglTouchable>
          <JglTouchable
            position='absolute'
            right={0}
            top={0}
            minW={22}
            minH={22}
            ai='center'
            jc='center'
            borderRadius={9999}
            onPress={() => {
              setUploadImageStatus('idle');
            }}
            p={2}
          >
            <JglView
              w={18}
              h={18}
              ai='center'
              jc='center'
              borderRadius={9999}
              backgroundColor='#0000004D'
              p={5}
            >
              <JglImage
                source={require('../assets/images/ic_ai_chat_delete_wait_send_image.png')}
                w={8}
                h={8}
              />
            </JglView>
          </JglTouchable>
        </JglTouchable>
      );
    } else if (uploadImageStatus === 'success' && image?.url) {
      return (
        <JglTouchable
          onPress={onPressPreviewWaitForSendImage}
          w={80}
          h={80}
          ai='center'
          jc='center'
          borderRadius={8}
          position='relative'
        >
          <JglImage
            resizeMode={'cover'}
            borderRadius={8}
            source={image.url}
            w='full'
            h='full'
          />
          <JglTouchable
            position='absolute'
            right={0}
            top={0}
            minW={22}
            minH={22}
            ai='center'
            jc='center'
            borderRadius={9999}
            onPress={onPressDeletePhoto}
            p={2}
          >
            <JglView
              w={18}
              h={18}
              ai='center'
              jc='center'
              borderRadius={9999}
              backgroundColor='#0000004D'
              p={5}
            >
              <JglImage
                source={require('../assets/images/ic_ai_chat_delete_wait_send_image.png')}
                w={8}
                h={8}
              />
            </JglView>
          </JglTouchable>
        </JglTouchable>
      );
    }
  }, [
    handleUploadImageRetry,
    image?.url,
    onPressDeletePhoto,
    onPressPreviewWaitForSendImage,
    uploadImageStatus,
  ]);

  // 使用 Reanimated 的 shared values 来管理状态
  const recordingButtonRect = useSharedValue<RecordingButtonRect | null>(null);
  const isWaitingForCancel = useSharedValue(false);
  const panY = useSharedValue(0);
  const isLongPressed = useSharedValue(false);

  // 处理录音按钮的布局变化
  const handleRecordingButtonLayout = useCallback(
    (event: LayoutChangeEvent) => {
      console.log(
        '🚀🚀🚀🚀🚀🚀 - leejunhui ~ JglAiQAInputToolBar ~ handleRecordingButtonLayout:',
        event.nativeEvent.layout,
      );
      const { x, y, width, height } = event.nativeEvent.layout;
      recordingButtonRect.value = { x, y, width, height };
    },
    [recordingButtonRect],
  );

  const updateFingerStillPressedState = useCallback((isPressed: boolean) => {
    isFingerStillPressedRef.current = isPressed;
  }, []);

  // Logging functions for gesture handlers
  const logPanBegin = useCallback(() => {
    console.log('🎤 Pan 手势 Begin');
  }, []);

  const logLongPressedValue = useCallback(
    (description?: string) => {
      console.log(
        '🎤 长按状态 - isLongPressed:',
        isLongPressed.value,
        description,
      );
    },
    [isLongPressed.value],
  );

  const logPanUpdate = useCallback(
    (x: number, y: number, absoluteX: number, absoluteY: number) => {
      console.log(
        '🎤 Pan onUpdate 手势更新 - x:',
        x,
        'y:',
        y,
        'absoluteX:',
        absoluteX,
        'absoluteY:',
        absoluteY,
      );
    },
    [],
  );

  const logRecordingStateUpdate = useCallback(
    (
      eventY: number,
      buttonY: number,
      isAboveButton: boolean,
      isWaitingForCancelValue: boolean,
    ) => {
      console.log(
        '🎤 录音手势状态 - y:',
        eventY,
        'buttonY:',
        buttonY,
        'isAboveButton:',
        isAboveButton,
        'isWaitingForCancel:',
        isWaitingForCancelValue,
      );
    },
    [],
  );

  const logEnterCancelState = useCallback(() => {
    console.log('🎤 进入取消录音状态');
  }, []);

  const logResumeRecordingState = useCallback(() => {
    console.log('🎤 恢复录音状态');
  }, []);

  const logPanFinalize = useCallback(() => {
    console.log('🎤 Pan 手势结束');
  }, []);

  const logGestureEndState = useCallback(
    (y: number, buttonY: number, isAboveButton: boolean) => {
      console.log(
        '🎤 手势结束 - y:',
        y,
        'buttonY:',
        buttonY,
        'isAboveButton:',
        isAboveButton,
      );
    },
    [],
  );

  const logGestureEndCancel = useCallback(() => {
    console.log('🎤 手势结束 - 在按钮上方,取消录音');
  }, []);

  const logGestureEndFinish = useCallback(() => {
    console.log('🎤 手势结束 - 在按钮下方,结束录音');
  }, []);

  const logLongPressEnd = useCallback((success: boolean) => {
    console.log('🎤 长按结束 logLongPressEnd  - 成功:', success);
  }, []);

  const logLongPressFinalize = useCallback((success: boolean) => {
    console.log('🎤 长按结束 logLongPressFinalize - 成功:', success);
  }, []);

  const logTapStart = useCallback(() => {
    console.log('🎤 Tap 手势开始');
  }, []);

  const logTapEnd = useCallback(() => {
    console.log('🎤 Tap 手势结束');
  }, []);

  // 组合手势
  const gesture = useMemo(() => {
    const longPressGesture = Gesture.LongPress()
      .minDuration(200)
      .onStart((event) => {
        'worklet';
        const eventData = {
          x: event.x,
          y: event.y,
          absoluteX: event.absoluteX,
          absoluteY: event.absoluteY,
        };
        runOnJS(updateFingerStillPressedState)(true);
        runOnJS(handleLongPress)(eventData);
      })
      .onEnd((event, success) => {
        'worklet';
        runOnJS(logLongPressEnd)(success);
        isLongPressed.value = true;
      })
      .onFinalize((event, success) => {
        'worklet';
        runOnJS(logLongPressFinalize)(success);
        runOnJS(updateFingerStillPressedState)(false);
      });

    const panGesture = Gesture.Pan()
      .cancelsTouchesInView(true)
      .onBegin((event) => {
        'worklet';
        runOnJS(logLongPressedValue)('pan begin');
        if (!isLongPressed.value) {
          return;
        }
        runOnJS(logPanBegin)();
      })
      .onUpdate((event) => {
        'worklet';
        runOnJS(logLongPressedValue)('pan update');
        if (!isLongPressed.value) {
          return;
        }
        runOnJS(logPanUpdate)(
          event.x,
          event.y,
          event.absoluteX,
          event.absoluteY,
        );

        if (recordingButtonRect.value) {
          const { y } = recordingButtonRect.value;
          const isAboveButton = event.y < y;

          runOnJS(logRecordingStateUpdate)(
            event.y,
            y,
            isAboveButton,
            isWaitingForCancel.value,
          );

          if (isAboveButton && !isWaitingForCancel.value) {
            runOnJS(logEnterCancelState)();
            isWaitingForCancel.value = true;
            runOnJS(transitionRecordingState)(JglAiQAVoiceState.WaitForCancel);
          } else if (!isAboveButton && isWaitingForCancel.value) {
            runOnJS(logResumeRecordingState)();
            isWaitingForCancel.value = false;
            runOnJS(transitionRecordingState)(JglAiQAVoiceState.Recording);
          }
        }
      })
      .onFinalize((event) => {
        'worklet';

        runOnJS(logLongPressedValue)('pan finalize');
        if (!isLongPressed.value) {
          return;
        }

        runOnJS(updateFingerStillPressedState)(false);
        runOnJS(logPanFinalize)();
        const eventData = {
          x: event.x,
          y: event.y,
          absoluteX: event.absoluteX,
          absoluteY: event.absoluteY,
        };

        if (recordingButtonRect.value) {
          const { y } = recordingButtonRect.value;
          const isAboveButton = event.y < y;

          runOnJS(logGestureEndState)(event.y, y, isAboveButton);

          const touchEvent = convertGestureToTouchEvent(eventData);

          if (isAboveButton) {
            runOnJS(logGestureEndCancel)();
            runOnJS(handleTouchCancel)(touchEvent);
          } else {
            runOnJS(logGestureEndFinish)();
            runOnJS(handleTouchEnd)(touchEvent);
          }
        } else {
          runOnJS(handleTouchEnd)(convertGestureToTouchEvent(eventData));
        }
        // 在滑动结束时重置长按状态
        isLongPressed.value = false;
      });

    const tapGesture = Gesture.Tap()
      .onStart((event) => {
        runOnJS(logTapStart)();
      })
      .onEnd((event) => {
        runOnJS(logTapEnd)();
      });

    return Gesture.Simultaneous(tapGesture, longPressGesture, panGesture);
  }, [
    updateFingerStillPressedState,
    handleLongPress,
    logLongPressEnd,
    isLongPressed,
    logLongPressFinalize,
    logLongPressedValue,
    logPanBegin,
    logPanUpdate,
    recordingButtonRect.value,
    logRecordingStateUpdate,
    isWaitingForCancel,
    logEnterCancelState,
    transitionRecordingState,
    logResumeRecordingState,
    logPanFinalize,
    logGestureEndState,
    logGestureEndCancel,
    handleTouchCancel,
    logGestureEndFinish,
    handleTouchEnd,
    logTapStart,
    logTapEnd,
  ]);

  // 动画样式
  const animatedStyle = useAnimatedStyle(() => {
    'worklet';
    return {
      transform: [
        {
          translateY: withSpring(panY.value, {
            damping: 15,
            stiffness: 150,
          }),
        },
      ],
    };
  });

  // 渲染录音按钮
  const renderVoiceInput = useMemo(() => {
    const commonStyle = {
      flex: 1,
      width: '100%',
      height: '100%',
      position: 'relative',
      zIndex: 1,
    } as const;

    const voiceInput = <VoiceInputComponent voiceState={voiceState} />;

    return agreementState === 'disagreed' ? (
      <JglTouchable style={commonStyle} onPress={withAgreementCheck()}>
        {voiceInput}
      </JglTouchable>
    ) : (
      <GestureDetector gesture={gesture}>
        <Animated.View
          onLayout={handleRecordingButtonLayout}
          style={[commonStyle, animatedStyle]}
        >
          {voiceInput}
        </Animated.View>
      </GestureDetector>
    );
  }, [
    voiceState,
    agreementState,
    withAgreementCheck,
    gesture,
    handleRecordingButtonLayout,
    animatedStyle,
  ]);

  const handleInputBlur = useCallback(() => {
    if (shouldClearInputRef.current) {
      setInputValue('');
      shouldClearInputRef.current = false;
    } else {
      const trimmedInputValue = inputValue?.trim?.() || '';
      if (trimmedInputValue.length === 0) {
        setInputValue('');
      }
    }
  }, [inputValue]);

  const renderKeyboardInput = useMemo(() => {
    const commonStyle = {
      flex: 1,
      width: '100%',
      height: '100%',
      position: 'relative',
      zIndex: 1,
    } as const;
    if (agreementState === 'disagreed') {
      return (
        <JglTouchable style={commonStyle} onPress={withAgreementCheck()}>
          <AutoHeightTextInput
            ref={inputRef}
            value={inputValue}
            onChangeText={setInputValue}
            placeholderText='什么都可以问我'
            maxLength={200}
            editable={false}
            placeholderStyle={{
              fontSize: 16,
              lineHeight: 24,
              color: '#6F6F6F',
            }}
            style={{
              flex: 1,
              lineHeight: 24,
              fontSize: 16,
              textAlignVertical: 'center',
            }}
            onBlur={handleInputBlur}
            // enterKeyHint="send"
            // onSubmitEditing={() => {
            //   console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - onSubmitEditing');
            // }}
          />
        </JglTouchable>
      );
    } else {
      return (
        <AutoHeightTextInput
          ref={inputRef}
          value={inputValue}
          onChangeText={setInputValue}
          placeholderText='什么都可以问我'
          maxLength={200}
          placeholderTextColor='#6F6F6F'
          // placeholderStyle={{
          //   fontSize: 16,
          //   lineHeight: 24,
          //   color: '#6F6F6F',
          // }}
          style={{
            flex: 1,
            lineHeight: 24,
            fontSize: 16,
            textAlignVertical: 'center',
          }}
          onBlur={handleInputBlur}
          returnKeyType='send'
          onSubmitEditing={handleSendPress}
          // returnKeyType="send"
          // enterKeyHint="send"
          // onSubmitEditing={() => {
          //   console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - onSubmitEditing');
          // }}
        />
      );
    }
  }, [
    agreementState,
    handleInputBlur,
    handleSendPress,
    inputValue,
    withAgreementCheck,
  ]);

  const renderRightButton = useMemo(() => {
    if (
      voiceState !== JglAiQAVoiceState.Idle &&
      voiceState !== JglAiQAVoiceState.Error &&
      voiceState !== JglAiQAVoiceState.Finished
    ) {
      return null;
    }

    const showSendButton = (inputValue?.length || 0) > 0 || !!image;
    const showSwitchButton = (inputValue?.length || 0) === 0; // 仅当输入为空时显示切换按钮

    if (showSendButton && showSwitchButton && image) {
      // 情况：选择了图片，没有输入文本 -> 显示切换和发送按钮
      return (
        <JglXStack ai='center' jc='center' space={10}>
          {renderSwitchInputButtonComponent}
          {renderSendButtonComponent}
        </JglXStack>
      );
    }
    if (showSendButton) {
      // 情况：存在输入文本（无论是否有图片） 或 只有图片 -> 只显示发送按钮
      return renderSendButtonComponent;
    }
    if (showSwitchButton) {
      // 情况：没有输入文本，没有图片 -> 只显示切换按钮
      return renderSwitchInputButtonComponent;
    }
    // 理论上如果逻辑正确，不会发生这种情况，但作为好的实践保留
    return null;
  }, [
    voiceState,
    inputValue,
    image,
    renderSendButtonComponent,
    renderSwitchInputButtonComponent,
  ]);

  const handleInputToolBarLayout = useCallback(
    (e: LayoutChangeEvent) => {
      console.log(
        'leejunhui - 🔥🔥🔥🔥🔥🔥 - handleInputToolBarLayout',
        e.nativeEvent.layout,
      );
      setJglAiQAInputToolBarHeight(e.nativeEvent.layout.height);
    },
    [setJglAiQAInputToolBarHeight],
  );

  const handlePressTakePhotoButton = useCallback(() => {
    if (voiceState === JglAiQAVoiceState.Finished) {
      return;
    }
    lightVibrate();
    onPressTakePhoto();
  }, [voiceState, onPressTakePhoto]);

  const showCameraButtonIfNeeded = useMemo(() => {
    if (inputState === JglAiQAInputState.Voice) {
      return (
        voiceState === JglAiQAVoiceState.Idle ||
        voiceState === JglAiQAVoiceState.Error ||
        voiceState === JglAiQAVoiceState.Finished
      );
    } else {
      return keyboardHeight === 0;
    }
  }, [inputState, voiceState, keyboardHeight]);

  const containerPaddingBottom = useMemo(() => {
    if (keyboardHeight > 0) {
      return 16;
    } else {
      return Platform.OS === 'ios' ? safeBottom || 16 : 20;
    }
  }, [keyboardHeight, safeBottom]);

  const handleOnOutMessageContextMenuEditButtonPress = useCallback(
    (event: { messageTextContent: string }) => {
      console.log(
        'leejunhui - 🔥🔥🔥🔥🔥🔥 - onOutMessageContextMenuEditButtonPress 来了哦',
        event,
      );
      setInputState(JglAiQAInputState.Keyboard);
      setShowEditMessageBar(true);
      setInputValue(event.messageTextContent);
      inputRef.current?.focus();
    },
    [setInputState, setShowEditMessageBar],
  );

  const handleChoosePhotoLibraryImage = useCallback(
    async (event: { url: string; width: number; height: number }) => {
      await handleImageUpload(event, 'photoLibrary');
    },
    [handleImageUpload],
  );

  const handleTakePhoto = useCallback(
    async (event: { url: string; width: number; height: number }) => {
      await handleImageUpload(event, 'camera');
    },
    [handleImageUpload],
  );

  const imLoginStatus = useAtomValue(imLoginStatusAtom);
  useEffect(() => {
    if (imLoginStatus === 'loggedIn') {
      eventBus.off(
        'onOutMessageContextMenuEditButtonPress',
        handleOnOutMessageContextMenuEditButtonPress,
      );
      eventBus.on(
        'onOutMessageContextMenuEditButtonPress',
        handleOnOutMessageContextMenuEditButtonPress,
      );
      eventBus.off('choosePhotoLibraryImage', handleChoosePhotoLibraryImage);
      eventBus.on('choosePhotoLibraryImage', handleChoosePhotoLibraryImage);
      eventBus.off('takePhoto', handleTakePhoto);
      eventBus.on('takePhoto', handleTakePhoto);
    }
    return () => {
      eventBus.off(
        'onOutMessageContextMenuEditButtonPress',
        handleOnOutMessageContextMenuEditButtonPress,
      );
      eventBus.off('choosePhotoLibraryImage', handleChoosePhotoLibraryImage);
      eventBus.off('takePhoto', handleTakePhoto);
    };
  }, [
    imLoginStatus,
    handleOnOutMessageContextMenuEditButtonPress,
    handleChoosePhotoLibraryImage,
    handleTakePhoto,
  ]);

  // 监听 voiceState 变化，在状态切换时触发 Haptic 反馈
  const prevVoiceStateRef = useRef(voiceState);
  useEffect(() => {
    const prevState = prevVoiceStateRef.current;
    const currentState = voiceState;

    // 当状态从 Recording 切换到 WaitForCancel 时触发震动
    if (
      prevState === JglAiQAVoiceState.Recording &&
      currentState === JglAiQAVoiceState.WaitForCancel
    ) {
      console.log('🎯 状态变化触发 Haptic 反馈: Recording -> WaitForCancel');
      lightVibrate();
    }
    // 当状态从 WaitForCancel 切换到 Recording 时触发震动
    else if (
      prevState === JglAiQAVoiceState.WaitForCancel &&
      currentState === JglAiQAVoiceState.Recording
    ) {
      console.log('🎯 状态变化触发 Haptic 反馈: WaitForCancel -> Recording');
      lightVibrate();
    }

    prevVoiceStateRef.current = currentState;
  }, [voiceState]);

  return (
    <>
      <JglYStack
        id={JglAiAQInputToolBarId}
        w='full'
        borderTopLeftRadius={16}
        borderTopRightRadius={16}
        p={12}
        pb={containerPaddingBottom}
        space={10}
        position='absolute'
        left={0}
        right={0}
        bottom={0}
        overflowY='hidden'
        zIndex={100}
        backgroundColor={backgroundColor ?? '$backgroundTransparent'}
        // @ts-ignore
        onLayout={handleInputToolBarLayout}
      >
        {renderHeader}
        {renderImage}
        {showEditMessageBar ? (
          <JglXStack
            bg='#D9E2FC'
            w='full'
            borderRadius={18}
            position='absolute'
            top={-24}
            left={12}
            right={12}
            zIndex={-1}
            pb={29}
          >
            <JglXStack
              w='full'
              h={34}
              px={16}
              py={6}
              ai='center'
              jc='space-between'
              borderTopLeftRadius={18}
              borderTopRightRadius={18}
            >
              <JglText fontSize={14} color={'$color12'} fontWeight='400'>
                编辑信息
              </JglText>
              <JglTouchable
                style={{
                  minWidth: 20,
                  minHeight: 20,
                }}
                onPress={() => {
                  setShowEditMessageBar(false);
                  setInputValue('');
                }}
              >
                <JglImage
                  source={require('../assets/images/ic_chat_input_tool_bar_edit_bar_close.png')}
                  w={20}
                  h={20}
                />
              </JglTouchable>
            </JglXStack>
          </JglXStack>
        ) : null}
        <JglXStack
          borderRadius={18}
          style={{
            shadowColor: '#000000',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.05,
            shadowRadius: 16,
            elevation: 16,
          }}
          backgroundColor='white'
          w='full'
          space={10}
          ai={'flex-end'}
          px={16}
          py={14}
          minH={64}
        >
          {/* 拍照按钮 */}
          {showCameraButtonIfNeeded ? (
            <JglTouchable
              w={26}
              h={26}
              ai='center'
              jc='center'
              onPress={withAgreementCheck(handlePressTakePhotoButton)}
            >
              <JglImage
                source={require('../assets/images/ic_ai_chat_camera.png')}
                w={26}
                h={26}
              />
            </JglTouchable>
          ) : null}

          {inputState === JglAiQAInputState.Voice ? (
            <JglYStack position='relative' flex={1}>
              {renderVoiceInput}
            </JglYStack>
          ) : (
            renderKeyboardInput
          )}

          {renderRightButton}
        </JglXStack>
      </JglYStack>
      <ImagePreviewerComponent />
    </>
  );
});

// 抽离单个音频条组件
const VoiceBar = memo(({ height }: { height: number }) => {
  const heightShared = useSharedValue(height);

  useEffect(() => {
    heightShared.value = withTiming(height, {
      duration: 100,
      easing: Easing.inOut(Easing.ease),
    });
  }, [height, heightShared]);

  const animatedStyle = useAnimatedStyle(() => ({
    width: 2,
    height: heightShared.value,
    backgroundColor: 'white',
    borderRadius: 2,
    flexShrink: 0,
  }));

  return <Animated.View style={animatedStyle} />;
});

const VoiceInputComponent = memo((props: { voiceState: JglAiQAVoiceState }) => {
  const { voiceState } = props;

  const windowSize = useWindowDimensions();
  const voiceBarCounts = useMemo(() => {
    const availableWidth = windowSize.width - 12 * 2 - 45 * 2;
    return Math.floor((availableWidth + 4) / 6);
  }, [windowSize.width]);

  const [barHeights, setBarHeights] = useState<number[]>(() =>
    Array(voiceBarCounts).fill(MIN_HEIGHT),
  );

  const generateRandomHeights = useCallback(() => {
    return Array(voiceBarCounts)
      .fill(0)
      .map(
        () =>
          Math.floor(Math.random() * (MAX_HEIGHT - MIN_HEIGHT + 0.2) * 0.8) +
          MIN_HEIGHT,
      );
  }, [voiceBarCounts]);

  useEffect(() => {
    let interval: number | null = null;
    const isAnimating =
      voiceState === JglAiQAVoiceState.Recording ||
      voiceState === JglAiQAVoiceState.WaitForCancel;

    if (isAnimating) {
      // @ts-ignore
      interval = setInterval(() => {
        setBarHeights(generateRandomHeights());
      }, 100);
    } else {
      // When not animating, reset to a calm state (all MIN_HEIGHT)
      setBarHeights(Array(voiceBarCounts).fill(MIN_HEIGHT));
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [voiceState, voiceBarCounts, generateRandomHeights]);

  if (
    voiceState === JglAiQAVoiceState.Idle ||
    voiceState === JglAiQAVoiceState.Error
  ) {
    return (
      <JglView flex={1} ai='center' jc='center'>
        <JglText fontSize={16} color={'$color12'} fontWeight='500'>
          按住 说话
        </JglText>
      </JglView>
    );
  }
  if (voiceState === JglAiQAVoiceState.Finished) {
    return (
      <JglView flex={1} ai='center' jc='center'>
        <LoadingDots baseText='正在发送' />
      </JglView>
    );
  }

  const isRecording = voiceState === JglAiQAVoiceState.Recording;
  const isWaitForCancel = voiceState === JglAiQAVoiceState.WaitForCancel;

  let tipTextColor = '$color11';
  let tipTextFontWeight = '400';
  let tipText = '松手发送 上移取消';
  let voiceBarBgColor = '$blue9';
  if (isRecording) {
    tipTextColor = '$color11';
    tipTextFontWeight = '400';
  } else if (isWaitForCancel) {
    tipTextColor = '#EF3138';
    tipTextFontWeight = '500';
    tipText = '松手取消';
    voiceBarBgColor = '#EF3138';
  }

  return (
    <JglYStack space={8} flex={1} ai='center' jc='center'>
      <JglText
        fontSize={16}
        color={tipTextColor}
        fontWeight={tipTextFontWeight as unknown as '400' | '500'}
      >
        {tipText}
      </JglText>
      <JglXStack
        w='full'
        h={54}
        ai='center'
        jc='center'
        bg={voiceBarBgColor}
        py={14}
        px={16}
        borderRadius={18}
        style={{
          shadowColor: '#19FFF6',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.08,
          shadowRadius: 8,
          elevation: 8,
        }}
        space={4}
      >
        {barHeights.map((height, index) => (
          <VoiceBar key={`voice-bar-${index}-${height}`} height={height} />
        ))}
      </JglXStack>
    </JglYStack>
  );
});

// 动态点 LoadingDots 组件
const LoadingDots = memo(
  ({
    baseText = '正在发送',
    dotCount = 3,
    interval = 400,
  }: {
    baseText?: string;
    dotCount?: number;
    interval?: number;
  }) => {
    const [count, setCount] = useState(1);
    useEffect(() => {
      const timer = setInterval(() => {
        setCount((prev) => (prev % dotCount) + 1);
      }, interval);
      return () => clearInterval(timer);
    }, [dotCount, interval]);
    return (
      <JglText fontSize={16} color={'$color12'} fontWeight='500'>
        {baseText}
        {Array(count).fill('.').join('')}
      </JglText>
    );
  },
);
