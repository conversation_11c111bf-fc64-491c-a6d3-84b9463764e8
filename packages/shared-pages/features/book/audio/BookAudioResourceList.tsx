import { BottomSheetSectionList } from '@gorhom/bottom-sheet';
import { useAppSelector } from '@jgl/biz-func';
import { showToast } from '@jgl/utils';
import { type useAudioPlayer } from 'expo-audio';
import { useSetAtom } from 'jotai';
import { memo, useCallback, useRef } from 'react';
import type {
  SectionList,
  SectionListData,
  SectionListRenderItemInfo,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { bookAudioPlayerCurrentPlayInfoAtom } from '../atom/audioAtoms';
import { SectionHeaderChapterLevel1 } from '../components/SectionHeaderChapterLevel1';
import { SectionHeaderChapterLevelOthers } from '../components/SectionHeaderChapterLevelOthers';
import { useSharedBookData } from '../context/SharedBookDataContext';
import { useSharedDraggableMenuContext } from '../context/SharedDraggableMenuContext';
import type { Chapter, ResourceWithPage } from '../dtos/AiBookDTO';
import { useBookResourcePlayInfo } from '../hooks/useBookResourcePlayInfo';
import { useBookRouterParams } from '../hooks/useBookRouterParams';
import { useScrollToSection } from '../hooks/useScrollToSection';
import { StudyTab } from '../types/AiBookEnum';
import { sectionListGetItemLayout } from '../utils/sectionListGetItemLayout';
import { BookAudioResourceListItem } from './BookAudioResourceListItem';

type Props = {
  audioPlayer?: ReturnType<typeof useAudioPlayer>;
  sectionData: SectionListData<ResourceWithPage, Chapter>[];
  updateCurrentPlayingAudioResKey?: (key: string) => void;
  activeTab?: StudyTab;
};

export const BookAudioResourceList = memo((props: Props) => {
  const {
    audioPlayer,
    sectionData,
    updateCurrentPlayingAudioResKey,
    activeTab,
  } = props;
  const safeAreaInsets = useSafeAreaInsets();
  const sectionListRef = useRef<SectionList<unknown, unknown>>(null);
  const keyExtractor = useCallback((item: ResourceWithPage, index: number) => {
    return `${item.resKey}_${index}`;
  }, []);

  const { setCurrentQuestion, bookInfoData: aiBookInfo } = useSharedBookData();
  const { currentPageId, currentChapterId, currentQuestionId } =
    useBookRouterParams();
  const currentPageIdRef = useRef<string | undefined>(currentPageId);
  const currentChapterIdRef = useRef<string | undefined>(currentChapterId);
  const currentQuestionIdRef = useRef<string | undefined>(currentQuestionId);
  const { getBookResourcePlayInfo } = useBookResourcePlayInfo();
  const setBookAudioPlayerCurrentPlayInfo = useSetAtom(
    bookAudioPlayerCurrentPlayInfoAtom,
  );
  const bookId = aiBookInfo?.id;
  const userInfo = useAppSelector((state) => state.userInfo);
  const { currentPlayingAudioResKey, switchActiveTabTo } =
    useSharedDraggableMenuContext();
  const pressedResourceRef = useRef<ResourceWithPage | undefined>(undefined);

  const handlePressResourceItem = useCallback(
    async (info: SectionListRenderItemInfo<ResourceWithPage, Chapter>) => {
      const { item: resource } = info;
      const isCurrentResourceBeingPlayed =
        currentPlayingAudioResKey === resource.resKey;
      // 1.判断点击的是否是当前资源
      if (isCurrentResourceBeingPlayed) {
        // 1.1 点击的当前资源
        if (audioPlayer?.currentStatus.playing) {
          // 1.1.1 如果当前资源正在播放，则暂停
          audioPlayer?.pause();
        } else {
          // 1.1.2 如果当前资源未在播放，则播放
          audioPlayer?.play();
        }
      } else {
        updateCurrentPlayingAudioResKey?.(resource.resKey);
        pressedResourceRef.current = resource;
        // 1.2 点击的非当前资源
        const response = await getBookResourcePlayInfo({
          resId: resource.resId || 0,
          resIdSign: resource.resIdSign || '',
        });
        if (response?.resId) {
          if (response.url) {
            audioPlayer?.pause();
            audioPlayer?.replace(response.url);
            audioPlayer?.play();
            if (resource.page?.id && resource.chapter?.id) {
              setCurrentQuestion?.({
                currentChapterId: resource.chapter.id ?? 0,
                currentPageId: resource.page?.id ?? 0,
                currentQuestionId: resource.pageItem?.itemCode,
              });
            }
          }
        } else {
          // 1.2.1 获取资源失败
          // TODO: chenfeng - 2025-07-19 提示获取资源失败
          showToast({
            title: '资源播放失败',
          });
        }
      }
    },
    [
      audioPlayer,
      currentPlayingAudioResKey,
      getBookResourcePlayInfo,
      setCurrentQuestion,
      updateCurrentPlayingAudioResKey,
    ],
  );

  const renderListItem = useCallback(
    (info: SectionListRenderItemInfo<ResourceWithPage, Chapter>) => {
      return (
        <BookAudioResourceListItem
          resource={info.item}
          onPressItem={() => handlePressResourceItem(info)}
        />
      );
    },
    [handlePressResourceItem],
  );

  const renderSectionHeader = useCallback(
    (section: SectionListData<ResourceWithPage, Chapter>) => {
      if (!section.title) {
        return null;
      }

      if (section.level === 1) {
        return (
          <SectionHeaderChapterLevel1
            backgroundColor='white'
            chapterName={section.title || ''}
          />
        );
      }

      return (
        <SectionHeaderChapterLevelOthers
          backgroundColor='white'
          chapterName={section.title || ''}
        />
      );
    },
    [],
  );

  useScrollToSection({
    sectionData,
    sectionListRef,
    currentTab: StudyTab.Audio,
  });

  return (
    <BottomSheetSectionList
      ref={sectionListRef}
      // 注意，这里 sectionList 不能再设置 flex:1，不然 web 上无法显示
      style={{ flex: 1 }}
      contentContainerStyle={{
        paddingBottom: safeAreaInsets.bottom + 12,
      }}
      sections={sectionData}
      renderSectionHeader={({ section }) => {
        return renderSectionHeader(section);
      }}
      renderItem={renderListItem}
      keyExtractor={keyExtractor}
      stickySectionHeadersEnabled
      // @ts-ignore
      getItemLayout={sectionListGetItemLayout({
        getItemHeight: (rowData, sectionIndex, rowIndex) => 74,
        getSectionHeaderHeight: () => 40, // The height of your section headers
      })}
      // bg='transparent'
    />
  );
});
