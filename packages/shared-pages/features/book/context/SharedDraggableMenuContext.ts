import { type AibookInfoApiGetResInfosDTO } from '@yunti-private/api-aibook';
import { type useAudioPlayer } from 'expo-audio';
import type { VideoPlayer } from 'expo-video';
import {
  createContext,
  type Dispatch,
  type SetStateAction,
  useContext,
} from 'react';
import type { SharedValue } from 'react-native-reanimated';
import { type ResourceWithPage } from '../dtos/AiBookDTO';
import { type StudyTab } from '../types/AiBookEnum';

export type SharedDraggableMenuContextValue = {
  activeTab?: StudyTab;
  switchActiveTabTo?: (toTab: StudyTab) => void;

  /* audio */
  audioPlayer?: ReturnType<typeof useAudioPlayer>;
  currentPlayingAudioResKey?: string;
  updateCurrentPlayingAudioResKey?: (resKey: string) => void;
  updateAudioPlayingStatus?: (status: 'playing' | 'idle') => void;
  audioItemsPlayStatus?: Record<string, 'playing' | 'idle' | 'buffering'>;
  updateAudioItemPlayStatus?: (param: {
    audioResKey: string;
    status: 'playing' | 'idle' | 'buffering';
  }) => void;

  /* video */
  inlineVideoPlayer?: VideoPlayer;
  fullscreenVideoPlayer?: VideoPlayer;
  inlineVideoPlaying: boolean;
  fullscreenVideoPlaying: boolean;
  selectedVideoResource?: ResourceWithPage;
  setSelectedVideoResource?: Dispatch<
    SetStateAction<ResourceWithPage | undefined>
  >;
  aiBookResInfo?: AibookInfoApiGetResInfosDTO;
  videoPlaybackRate: number;
  currentVideoPlayer?: VideoPlayer;
  videoFullscreen: boolean;
  setVideoFullscreen?: Dispatch<SetStateAction<boolean>>;
  videoCurrentTime?: SharedValue<number>;
  isVideoFinished?: SharedValue<boolean>;
};

export const SharedDraggableMenuContext =
  createContext<SharedDraggableMenuContextValue>({
    audioPlayer: undefined,
    currentPlayingAudioResKey: undefined,
    updateCurrentPlayingAudioResKey: undefined,
    updateAudioPlayingStatus: undefined,
    audioItemsPlayStatus: undefined,
    updateAudioItemPlayStatus: undefined,
    inlineVideoPlaying: false,
    fullscreenVideoPlaying: false,
    videoPlaybackRate: 1,
    videoFullscreen: false,
  });

export const useSharedDraggableMenuContext = () => {
  return useContext(SharedDraggableMenuContext);
};
