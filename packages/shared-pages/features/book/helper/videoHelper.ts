/** 格式化时间为 HH:MM:SS 格式 */
export const formatVideoTime = (seconds: number | undefined): string => {
  'worklet';

  if (!seconds) {
    return '00:00:00';
  }

  // 如果传入的float大于某个整数，则视为自身+1的整数
  // 例如：11.1887538 视为 12
  const processedSeconds =
    seconds > Math.floor(seconds)
      ? Math.floor(seconds) + 1
      : Math.floor(seconds);

  const hours = Math.floor(processedSeconds / 3600);
  const minutes = Math.floor((processedSeconds % 3600) / 60);
  const secs = Math.floor(processedSeconds % 60);

  return `${hours.toString().padStart(2, '0')}:${minutes
    .toString()
    .padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

/** 将当前播放时间转换为进度百分比 */
export const calculateProgress = (
  current: number | undefined,
  total: number | undefined,
): number => {
  'worklet';

  // 处理 NaN 和无效值的情况
  if (
    current === undefined ||
    total === undefined ||
    total <= 0 ||
    current < 0
  ) {
    return 0;
  }

  return Math.round(Math.min((current / total) * 100, 100));
};
