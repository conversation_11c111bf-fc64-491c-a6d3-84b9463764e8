import {
  YTCustomSheet,
  YTImage,
  YTText,
  YTTouchable,
  YTXStack,
  YTYStack,
} from '@bookln/cross-platform-components';
import { useBookAiCurrentBookId } from '@jgl/ai-qa-v2';
import { aibookInfoApiGetResInfos } from '@yunti-private/api-aibook';
import { useApiQuery } from '@yunti-private/net-query-hooks';
import { useAudioPlayer } from 'expo-audio';
import { useAtom } from 'jotai';
import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  FlatList,
  type LayoutChangeEvent,
  type ListRenderItem,
  type NativeScrollEvent,
  type NativeSyntheticEvent,
  Platform,
  useWindowDimensions,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { BookAITab } from '../ai/BookAITab';
import { bookStudyDraggableMenuHeightPercentageAtom } from '../atom/bookAtoms';
import { BookAudioTab } from '../audio/BookAudioTab';
import { useSharedBookData } from '../context/SharedBookDataContext';
import {
  SharedDraggableMenuContext,
  type SharedDraggableMenuContextValue,
} from '../context/SharedDraggableMenuContext';
import { bookEventBus } from '../event/eventBus';
import { useFlattenChaptersToSections } from '../helper/bookDataHelper';
import { AIBookFeatureType, ResType, StudyTab } from '../types/AiBookEnum';
import { BookVideoTab } from '../video/BookVideoTab';
import { useVideoPlayerInit } from '../video/hooks/useVideoPlayerInit';

// featureType -> StudyTab 映射
const featureTypeToTab: Partial<Record<AIBookFeatureType, StudyTab>> = {
  [AIBookFeatureType.AUDIO]: StudyTab.Audio,
  [AIBookFeatureType.VIDEO]: StudyTab.Video,
  [AIBookFeatureType.STUDY_PARTNER]: StudyTab.AI,
};

type StudyTabItem = {
  title: string;
  id: StudyTab;
  renderContent: React.JSX.Element;
};

type Props = {
  featureType?: AIBookFeatureType;
};

export const StudyDraggableMenu = memo((props: Props) => {
  const { featureType } = props;
  const safeInsets = useSafeAreaInsets();
  const windowHeight = useWindowDimensions().height;
  const windowWidth = useWindowDimensions().width;
  const [heightPercentage, setHeightPercentage] = useAtom(
    bookStudyDraggableMenuHeightPercentageAtom,
  );
  const { bookInfoData } = useSharedBookData();
  const { sections: allAudioSectionData, allResources: allAudioResources } =
    useFlattenChaptersToSections([ResType.AI_SOUND, ResType.SOUND]);
  const { allResources: allVideoResources } = useFlattenChaptersToSections([
    ResType.VIDEO,
    ResType.EASY_EXPLAIN,
  ]);
  const [tabBarHeight, setTabBarHeight] = useState(0);
  const [audioPlayingStatus, setAudioPlayingStatus] = useState<
    'playing' | 'idle'
  >('idle');
  const [audioItemsPlayStatus, setAudioItemsPlayStatus] = useState<
    Record<string, 'playing' | 'idle' | 'buffering'>
  >({});
  const [currentPlayingAudioResKey, setCurrentPlayingAudioResKey] = useState<
    string | undefined
  >(undefined);

  const flatListRef = useRef<FlatList<StudyTabItem>>(null);
  const tarBarHeightDeterminedRef = useRef(false);
  // 新增 scroll debounce timer
  const scrollTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const { bookAiCurrentBookId } = useBookAiCurrentBookId();

  const audioPlayer = useAudioPlayer();

  const {
    isVideoPlaying,
    inlineVideoPlayer,
    fullscreenVideoPlayer,
    selectedVideoResource,
    inlineVideoPlaying,
    fullscreenVideoPlaying,
    videoPlaybackRate,
    currentVideoPlayer,
    videoFullscreen,
    videoCurrentTime,
    isVideoFinished,
    setSelectedVideoResource,
    setVideoFullscreen,
  } = useVideoPlayerInit();

  const { data: aiBookResInfo } = useApiQuery(
    aibookInfoApiGetResInfos,
    {
      bookId: Number(bookInfoData?.bookId),
      bookIdSign: `${bookInfoData?.bookIdSign}`,
    },
    { enabled: !!bookInfoData?.bookId && !!bookInfoData.bookIdSign },
  );

  const handleLocateAudioResourceInList = useCallback(
    (param: { chapterId: number; pageId: number; problemId?: number }) => {
      const { chapterId, pageId, problemId } = param;
      console.log(
        'handleLocateAudioResourceInList',
        chapterId,
        pageId,
        problemId,
      );
    },
    [],
  );

  useEffect(() => {
    bookEventBus.on(
      'locateAudioResourceInList',
      handleLocateAudioResourceInList,
    );
    return () => {
      bookEventBus.off(
        'locateAudioResourceInList',
        handleLocateAudioResourceInList,
      );
    };
  }, [handleLocateAudioResourceInList]);

  const updateAudioPlayingStatus = useCallback((status: 'playing' | 'idle') => {
    setAudioPlayingStatus(status);
  }, []);

  const updateAudioItemPlayStatus = useCallback(
    (param: {
      audioResKey: string;
      status: 'playing' | 'idle' | 'buffering';
    }) => {
      const { audioResKey, status } = param;
      setAudioItemsPlayStatus((prev) => {
        const newStatus: Record<string, 'playing' | 'idle' | 'buffering'> = {};
        for (const key in prev) {
          newStatus[key] = 'idle';
        }
        newStatus[audioResKey] = status;
        return newStatus;
      });
    },
    [],
  );

  const updateCurrentPlayingAudioResKey = useCallback((resKey: string) => {
    setCurrentPlayingAudioResKey(resKey);
  }, []);

  const onTabBarLayout = useCallback((event: LayoutChangeEvent) => {
    if (!tarBarHeightDeterminedRef.current) {
      setTabBarHeight(event.nativeEvent.layout.height);
      tarBarHeightDeterminedRef.current = true;
    }
  }, []);

  const renderBookAITab = useMemo(() => {
    return <BookAITab />;
  }, []);

  const renderBookAudioTab = useMemo(() => {
    return <BookAudioTab featureType={featureType} />;
  }, [featureType]);

  const renderBookVideoTab = useMemo(() => {
    return <BookVideoTab />;
  }, []);

  const aiPartnerEnabled = useMemo(
    () => !!bookAiCurrentBookId,
    [bookAiCurrentBookId],
  );

  const audioTabEnabled = useMemo(() => {
    // 根据aiBookResInfo中的音频资源判断是否启用音频tab
    return allAudioResources.length > 0;
  }, [allAudioResources.length]);

  const videoTabEnabled = useMemo(() => {
    // 根据aiBookResInfo中的视频资源判断是否启用视频tab
    return allVideoResources.length > 0;
  }, [allVideoResources.length]);

  const tabList: StudyTabItem[] = useMemo(() => {
    const result = [];

    // 只有在启用视频的情况下才添加视频tab
    if (videoTabEnabled) {
      result.push({
        title: '视频',
        id: StudyTab.Video,
        renderContent: renderBookVideoTab,
      });
    }

    // 只有在启用音频的情况下才添加音频tab
    if (audioTabEnabled) {
      result.push({
        title: '音频',
        id: StudyTab.Audio,
        renderContent: renderBookAudioTab,
      });
    }

    // AI学伴tab的处理逻辑保持不变
    if (aiPartnerEnabled) {
      return [
        {
          title: 'AI学伴',
          id: StudyTab.AI,
          renderContent: renderBookAITab,
        },
        ...result,
      ];
    }

    return result;
  }, [
    aiPartnerEnabled,
    audioTabEnabled,
    renderBookAITab,
    renderBookAudioTab,
    renderBookVideoTab,
    videoTabEnabled,
  ]);

  const [activeTabIndex, setActiveTabIndex] = useState<number>(() => {
    if (featureType) {
      const mappedTab = featureTypeToTab[featureType as AIBookFeatureType];
      if (mappedTab) {
        const idx = tabList.findIndex((tab) => tab.id === mappedTab);
        return idx === -1 ? 0 : idx;
      }
    }
    return 0;
  });

  const activeTab = useMemo(() => {
    return tabList.at(activeTabIndex)?.id;
  }, [activeTabIndex, tabList]);

  const switchActiveTabTo = useCallback(
    (toTab: StudyTab) => {
      const tabIndex = tabList.findIndex((tab) => tab.id === toTab);
      if (tabIndex >= 0 && tabIndex < tabList.length) {
        setActiveTabIndex(tabIndex);
      }
    },
    [tabList],
  );

  const tabNames = useMemo(() => {
    return tabList.map((tab) => tab.title);
  }, [tabList]);

  const defaultInitialHeight = useMemo(
    () =>
      ((Math.max(64, tabBarHeight) + safeInsets.bottom) / windowHeight) * 100,
    [safeInsets.bottom, tabBarHeight, windowHeight],
  );

  const initialHeight = useMemo(() => {
    if (heightPercentage === undefined) {
      return defaultInitialHeight;
    } else {
      heightPercentage;
    }
  }, [heightPercentage, defaultInitialHeight]);

  const minHeight = useMemo(() => {
    return defaultInitialHeight;
  }, [defaultInitialHeight]);

  const maxHeight = useMemo(() => {
    return Platform.OS === 'ios'
      ? ((windowHeight * (645 / 812) + safeInsets.bottom) / windowHeight) * 100
      : ((windowHeight * 0.85 + safeInsets.bottom) / windowHeight) * 100;
  }, [safeInsets.bottom, windowHeight]);

  const handleHeightChange = useCallback(
    (heightPercentage: number) => {
      setHeightPercentage(heightPercentage);
    },
    [setHeightPercentage],
  );

  const renderCustomTabBar = useMemo(() => {
    return (
      <YTXStack
        gap={12}
        pb={16}
        w='100%'
        px={16}
        ai='center'
        onLayout={onTabBarLayout}
        bg='white'
      >
        {tabNames.map((tabName, index) => {
          const isFocused = index === activeTabIndex;
          return (
            <YTTouchable
              key={tabName}
              bg={isFocused ? '$accent9' : '$slBackground'}
              px={18}
              py={5}
              ai='center'
              jc='center'
              borderRadius={100}
              onPress={() => {
                console.log('🚀 ~ onPress ~ tabName:', tabName);
                flatListRef.current?.scrollToIndex({
                  index: tabNames.indexOf(tabName),
                  animated: true,
                });
              }}
              flexDirection='row'
              width={88}
              gap={4}
            >
              <YTText fontSize={14} color={isFocused ? 'white' : '$gray11'}>
                {tabName}
              </YTText>

              {audioPlayingStatus === 'playing' && tabName === '音频' ? (
                <YTImage
                  source={
                    isFocused
                      ? require('../../../assets/images/ic_playing_white.gif')
                      : require('../../../assets/images/ic_playing_theme.gif')
                  }
                  width={20}
                  height={20}
                />
              ) : null}

              {isVideoPlaying && tabName === '视频' ? (
                <YTImage
                  source={
                    isFocused
                      ? require('../../../assets/images/ic_playing_white.gif')
                      : require('../../../assets/images/ic_playing_theme.gif')
                  }
                  width={20}
                  height={20}
                />
              ) : null}
            </YTTouchable>
          );
        })}
      </YTXStack>
    );
  }, [
    activeTabIndex,
    audioPlayingStatus,
    isVideoPlaying,
    onTabBarLayout,
    tabNames,
  ]);

  const handleScroll = useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      const offsetX = event.nativeEvent.contentOffset.x;
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
      scrollTimeoutRef.current = setTimeout(() => {
        const page = Math.round(offsetX / windowWidth);
        if (page !== activeTabIndex) {
          setActiveTabIndex(page);
        }
      }, 100); // 100ms 防抖
    },
    [activeTabIndex, windowWidth],
  );

  const handleMomentumScrollEnd = useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      const offsetX = event.nativeEvent.contentOffset.x;
      const page = Math.round(offsetX / windowWidth);
      if (page !== activeTabIndex) {
        setActiveTabIndex(page);
      }
    },
    [activeTabIndex, windowWidth],
  );

  const renderFlatListItem = useCallback<ListRenderItem<StudyTabItem>>(
    ({ item }) => {
      return (
        <YTYStack
          style={{ flex: 1 }}
          width={windowWidth} /* 保证每页宽度一致 */
        >
          {item.renderContent}
        </YTYStack>
      );
    },
    [windowWidth],
  );

  const getItemLayout = useCallback(
    (_data: ArrayLike<StudyTabItem> | undefined | null, index: number) => ({
      length: windowWidth,
      offset: windowWidth * index,
      index,
    }),
    [windowWidth],
  );

  const renderPagerView = useMemo(() => {
    return (
      <FlatList
        ref={flatListRef}
        data={tabList}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item) => String(item.id)}
        renderItem={renderFlatListItem}
        style={{ flex: 1 }}
        contentContainerStyle={{ flexGrow: 1 }}
        initialScrollIndex={activeTabIndex}
        getItemLayout={getItemLayout}
        onMomentumScrollEnd={handleMomentumScrollEnd} // 移除
        scrollEventThrottle={16}
        extraData={activeTabIndex}
        removeClippedSubviews={false}
        initialNumToRender={tabList.length}
      />
    );
  }, [
    tabList,
    renderFlatListItem,
    activeTabIndex,
    getItemLayout,
    handleMomentumScrollEnd,
  ]);

  // tab 切换时同步 FlatList 滚动
  useEffect(() => {
    if (flatListRef.current) {
      if (activeTabIndex >= 0 && activeTabIndex < tabList.length) {
        setTimeout(() => {
          if (activeTabIndex >= 0 && activeTabIndex < tabList.length) {
            flatListRef.current?.scrollToIndex({
              index: activeTabIndex,
              animated: true,
            });
          }
        }, 0);
      }
    } else {
      setTimeout(() => {
        if (activeTabIndex >= 0 && activeTabIndex < tabList.length) {
          flatListRef.current?.scrollToIndex({
            index: activeTabIndex,
            animated: true,
          });
        }
      }, 0);
    }
  }, [activeTabIndex, tabList.length]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  const contextValue = useMemo((): SharedDraggableMenuContextValue => {
    return {
      switchActiveTabTo,

      audioPlayer,
      updateAudioPlayingStatus,
      audioItemsPlayStatus,
      updateAudioItemPlayStatus,
      currentPlayingAudioResKey,
      updateCurrentPlayingAudioResKey,
      aiBookResInfo,
      activeTab,

      /* video 相关 */
      inlineVideoPlayer,
      fullscreenVideoPlayer,
      selectedVideoResource,
      setSelectedVideoResource,
      inlineVideoPlaying,
      fullscreenVideoPlaying,
      videoPlaybackRate,
      currentVideoPlayer,
      videoFullscreen,
      setVideoFullscreen,
      isVideoFinished,
      videoCurrentTime,
    };
  }, [
    activeTab,
    aiBookResInfo,
    audioItemsPlayStatus,
    audioPlayer,
    currentPlayingAudioResKey,
    currentVideoPlayer,
    fullscreenVideoPlayer,
    fullscreenVideoPlaying,
    inlineVideoPlayer,
    inlineVideoPlaying,
    isVideoFinished,
    selectedVideoResource,
    setSelectedVideoResource,
    setVideoFullscreen,
    switchActiveTabTo,
    updateAudioItemPlayStatus,
    updateAudioPlayingStatus,
    updateCurrentPlayingAudioResKey,
    videoCurrentTime,
    videoFullscreen,
    videoPlaybackRate,
  ]);

  useEffect(() => {
    /*当前不是视频页，则暂停 */
    if (activeTab !== StudyTab.Video) {
      currentVideoPlayer.pause();
    }

    /*当前不是音频页，则暂停音频 */
    if (activeTab !== StudyTab.Audio) {
      audioPlayer.pause();
    }
  }, [activeTab, audioPlayer, currentVideoPlayer]);

  if (tabList.length === 0) {
    return null;
  }

  return (
    <SharedDraggableMenuContext.Provider value={contextValue}>
      <YTCustomSheet
        initialHeight={initialHeight}
        minHeight={minHeight}
        maxHeight={maxHeight}
        visible={true}
        rightCloseButton={'none'}
        showBackdrop={false}
        dragHandlerBackgroundColor='white'
        onHeightChange={handleHeightChange}
      >
        <YTYStack w='$full' style={{ flex: 1 }}>
          {renderCustomTabBar}
          {renderPagerView}
        </YTYStack>
      </YTCustomSheet>
    </SharedDraggableMenuContext.Provider>
  );
});
