import { YTYStack } from '@bookln/cross-platform-components';
import { type MediaResourceServicePlayDTO } from '@jgl/biz-func';
import { VideoView } from 'expo-video';
import {
  forwardRef,
  Fragment,
  memo,
  useCallback,
  useImperativeHandle,
  useRef,
} from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import { useSharedDraggableMenuContext } from '../context/SharedDraggableMenuContext';
import { FullscreenVideoPlayer } from './components/FullscreenVideoPlayer';
import { PlayPauseButton } from './components/PlayPauseButton';
import { VideoControls } from './components/VideoControls';
import { VideoInfo } from './components/VideoInfo';
import { useBookVideoTabContext } from './contexts';
import { useVideoPlayerSync } from './hooks/useVideoPlayerSync';

export type BookVideoPlayerRef = {
  play: (
    resInfo: MediaResourceServicePlayDTO,
    autoPlay?: boolean,
  ) => Promise<void>;
  pause: () => void;
};

const BookVideoPlayerComponent = forwardRef<BookVideoPlayerRef>((_, ref) => {
  const videoViewRef = useRef<VideoView>(null);

  const {
    isVideoFinished,
    videoCurrentTime,
    videoPlaybackRate,
    inlineVideoPlayer,
    videoFullscreen,
    inlineVideoPlaying,
  } = useSharedDraggableMenuContext();

  const { isControlsVisible, setIsControlsVisible } = useBookVideoTabContext();

  const {
    videoDuration,
    playVideo,
    pauseVideo,
    handlePressPlayOrPause,
    handleEnterFullscreen,
    handleExitFullscreen,
    handleSeek,
    handleChangePlaybackRate,
  } = useVideoPlayerSync();

  // 处理点击视频区域的逻辑
  const handleVideoAreaPress = useCallback(() => {
    setIsControlsVisible?.((prev) => !prev);
  }, [setIsControlsVisible]);

  useImperativeHandle(ref, () => ({ play: playVideo, pause: pauseVideo }));

  return (
    <Fragment>
      <YTYStack>
        <YTYStack
          w='100%'
          aspectRatio={16 / 9}
          background='$gray2'
          jc='flex-end'
          position='relative'
        >
          {inlineVideoPlayer ? (
            <VideoView
              ref={videoViewRef}
              player={inlineVideoPlayer}
              style={styles.videoView}
              nativeControls={false}
              allowsFullscreen={false}
              allowsPictureInPicture={false}
              surfaceType={'surfaceView'}
            />
          ) : null}

          <TouchableOpacity
            style={styles.videoTouchArea}
            activeOpacity={1}
            onPress={handleVideoAreaPress}
          />

          <VideoControls
            currentTime={videoCurrentTime}
            duration={videoDuration}
            isFullscreen={videoFullscreen}
            isVideoFinished={isVideoFinished}
            playbackRate={videoPlaybackRate}
            isVisible={isControlsVisible}
            onFullscreenToggle={handleEnterFullscreen}
            onSeek={handleSeek}
            handleChangePlaybackRate={handleChangePlaybackRate}
          />

          <PlayPauseButton
            isPlaying={inlineVideoPlaying}
            color='white'
            isVisible={isControlsVisible}
            onPress={handlePressPlayOrPause}
          />
        </YTYStack>

        <VideoInfo duration={videoDuration} />
      </YTYStack>

      <FullscreenVideoPlayer
        isFullscreen={videoFullscreen}
        currentTime={videoCurrentTime}
        duration={videoDuration}
        playbackRate={videoPlaybackRate}
        onExitFullscreen={handleExitFullscreen}
        onPlayPause={handlePressPlayOrPause}
        onSeek={handleSeek}
        handleChangePlaybackRate={handleChangePlaybackRate}
      />
    </Fragment>
  );
});

const styles = StyleSheet.create({
  videoView: {
    width: '100%',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'black',
  },
  videoTouchArea: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 5,
  },
});

export const BookVideoPlayer = memo(BookVideoPlayerComponent);
