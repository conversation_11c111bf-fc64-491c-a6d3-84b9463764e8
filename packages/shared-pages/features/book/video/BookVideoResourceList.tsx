import { BottomSheetSectionList } from '@gorhom/bottom-sheet';
import { forwardRef, memo, useCallback } from 'react';
import {
  StyleSheet,
  type SectionList,
  type SectionListData,
  type SectionListRenderItemInfo,
} from 'react-native';
import { SectionHeaderChapterLevel1 } from '../components/SectionHeaderChapterLevel1';
import { SectionHeaderChapterLevelOthers } from '../components/SectionHeaderChapterLevelOthers';
import { type Chapter, type ResourceWithPage } from '../dtos/AiBookDTO';
import { BookVideoResourceListItem } from './BookVideoResourceListItem';

type Props = {
  sectionData: Array<SectionListData<ResourceWithPage, Chapter>>;
};

const BookVideoResourceListComponent = forwardRef<
  SectionList<unknown, unknown>,
  Props
>((props, ref) => {
  const { sectionData } = props;

  const keyExtractor = useCallback((item: ResourceWithPage, index: number) => {
    return `${item.resKey}_${index}`;
  }, []);

  const renderListItem = useCallback(
    (info: SectionListRenderItemInfo<ResourceWithPage, Chapter>) => {
      return <BookVideoResourceListItem resource={info.item} />;
    },
    [],
  );

  const renderSectionHeader = useCallback(
    (section: SectionListData<ResourceWithPage, Chapter>) => {
      if (!section.title) {
        return null;
      }

      if (section.level === 1) {
        return <SectionHeaderChapterLevel1 chapterName={section.title || ''} />;
      }

      return (
        <SectionHeaderChapterLevelOthers chapterName={section.title || ''} />
      );
    },
    [],
  );

  return (
    <BottomSheetSectionList
      ref={ref}
      style={styles.sectionList}
      sections={sectionData}
      renderSectionHeader={({ section }) => {
        return renderSectionHeader(section);
      }}
      renderItem={renderListItem}
      keyExtractor={keyExtractor}
      stickySectionHeadersEnabled
    />
  );
});

const styles = StyleSheet.create({
  sectionList: {
    flex: 1,
  },
});

export const BookVideoResourceList = memo(BookVideoResourceListComponent);
