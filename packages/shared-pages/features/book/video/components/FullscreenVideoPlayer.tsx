import {
  YTTouchable,
  YTXStack,
  YTYStack,
} from '@bookln/cross-platform-components';
import { ArrowLeft } from '@bookln/icon-lucide';
import { useWindowDimensions } from '@jgl/biz-func';
import { VideoView } from 'expo-video';
import { memo, useCallback, useEffect, useRef } from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import Modal from 'react-native-modal';
import type { SharedValue } from 'react-native-reanimated';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useSharedDraggableMenuContext } from '../../context/SharedDraggableMenuContext';
import { useBookVideoTabContext } from '../contexts';
import { PlayPauseButton } from './PlayPauseButton';
import { VideoControls } from './VideoControls';

export interface FullscreenVideoPlayerProps {
  isFullscreen: boolean;
  currentTime: SharedValue<number> | undefined;
  duration: number;
  playbackRate: number;
  onExitFullscreen: () => void;
  onPlayPause: () => void;
  onSeek?: (value: number) => void;
  handleChangePlaybackRate: (rate: number) => void;
}

export const FullscreenVideoPlayer = memo<FullscreenVideoPlayerProps>(
  (props) => {
    const {
      isFullscreen,
      currentTime,
      duration,
      playbackRate,
      onExitFullscreen,
      onPlayPause,
      onSeek,
      handleChangePlaybackRate,
    } = props;

    const videoViewRef = useRef<VideoView>(null);

    const { width: screenWidth, height: screenHeight } = useWindowDimensions();

    const {
      top: safeTop,
      bottom: safeBottom,
      left: safeLeft,
    } = useSafeAreaInsets();

    const { isControlsVisible, setIsControlsVisible } =
      useBookVideoTabContext();

    const { fullscreenVideoPlayer, fullscreenVideoPlaying } =
      useSharedDraggableMenuContext();

    // 顶部栏透明度动画
    const topBarOpacity = useSharedValue(isControlsVisible ? 1 : 0);

    // 顶部栏动画样式
    const topBarAnimatedStyle = useAnimatedStyle(() => {
      return {
        opacity: withTiming(topBarOpacity.value, { duration: 300 }),
        transform: [
          {
            translateY: withTiming(topBarOpacity.value === 0 ? -20 : 0, {
              duration: 300,
            }),
          },
        ],
      };
    });

    const rotatedSafeLeft = safeTop;
    const rotatedSafeRight = safeBottom;

    // 处理点击视频区域的逻辑
    const handleVideoAreaPress = useCallback(() => {
      setIsControlsVisible?.((prev) => !prev);
    }, [setIsControlsVisible]);

    useEffect(() => {
      topBarOpacity.value = isControlsVisible ? 1 : 0;
    }, [isControlsVisible, topBarOpacity]);

    return (
      <Modal
        isVisible={isFullscreen}
        style={styles.modal}
        statusBarTranslucent
        coverScreen
        animationIn='fadeIn'
        animationOut='fadeOut'
        hardwareAccelerated
        useNativeDriver
        hideModalContentWhileAnimating
        onBackButtonPress={onExitFullscreen}
        supportedOrientations={['portrait', 'landscape']}
      >
        <YTYStack
          flex={1}
          backgroundColor='black'
          justifyContent='center'
          alignItems='center'
        >
          <YTYStack
            style={styles.rotatedContainer}
            width={screenHeight - rotatedSafeLeft - rotatedSafeRight}
            height={screenWidth}
            position='relative'
            justifyContent='center'
            alignItems='center'
          >
            {fullscreenVideoPlayer ? (
              <VideoView
                ref={videoViewRef}
                player={fullscreenVideoPlayer}
                style={styles.fullscreenVideoView}
                contentFit='contain'
                nativeControls={false}
                allowsFullscreen={false}
                allowsPictureInPicture={false}
                /* 允许旋转 */
                surfaceType={'textureView'}
              />
            ) : null}

            <TouchableOpacity
              style={styles.videoTouchArea}
              activeOpacity={1}
              onPress={handleVideoAreaPress}
            />

            <YTYStack
              position='absolute'
              top={safeLeft}
              left={0}
              right={0}
              width='100%'
              bg='transparent'
              zIndex={10}
            >
              <Animated.View
                style={[styles.topBarContainer, topBarAnimatedStyle]}
              >
                <YTTouchable activeOpacity={1}>
                  <YTYStack bg='rgba(0,0,0,0.45)' ai='center' width='100%'>
                    <YTXStack
                      alignItems='center'
                      justifyContent='flex-start'
                      width='100%'
                      bg='transparent'
                      p={8}
                      borderTopWidth={1}
                      borderTopColor='#FFFFFF87'
                      borderStyle='solid'
                    >
                      <YTTouchable
                        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                        onPress={onExitFullscreen}
                      >
                        <ArrowLeft color='#FCFCFC' size={24} />
                      </YTTouchable>
                    </YTXStack>
                  </YTYStack>
                </YTTouchable>
              </Animated.View>
            </YTYStack>

            <YTYStack
              position='absolute'
              bottom={safeLeft}
              left={0}
              right={0}
              width='100%'
              bg='transparent'
              zIndex={10}
            >
              <VideoControls
                currentTime={currentTime}
                duration={duration}
                playbackRate={playbackRate}
                isFullscreen={isFullscreen}
                isVisible={isControlsVisible}
                onFullscreenToggle={onExitFullscreen}
                onSeek={onSeek}
                handleChangePlaybackRate={handleChangePlaybackRate}
              />
            </YTYStack>

            <PlayPauseButton
              isPlaying={fullscreenVideoPlaying}
              color='white'
              isVisible={isControlsVisible}
              onPress={onPlayPause}
            />
          </YTYStack>
        </YTYStack>
      </Modal>
    );
  },
);

const styles = StyleSheet.create({
  modal: {
    margin: 0,
  },
  rotatedContainer: {
    transform: [{ rotate: '90deg' }],
  },
  fullscreenVideoView: {
    backgroundColor: 'black',
    width: '100%',
    height: '100%',
    alignSelf: 'center',
  },
  videoTouchArea: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 5,
  },
  topBarContainer: {
    width: '100%',
    zIndex: 10,
  },
});
