import { useEvent, useEventListener } from 'expo';
import { useVideoPlayer } from 'expo-video';
import { useEffect, useMemo, useState } from 'react';
import { InteractionManager } from 'react-native';
import { useAnimatedReaction, useSharedValue } from 'react-native-reanimated';
import { VIDEO_EVENT_UPDATE_TIME } from '../../constants/video';
import { type ResourceWithPage } from '../../dtos/AiBookDTO';

/** 视频播放器初始化逻辑 */
export const useVideoPlayerInit = () => {
  const [selectedVideoResource, setSelectedVideoResource] =
    useState<ResourceWithPage>();
  const [videoPlaybackRate, setVideoPlaybackRate] = useState(1);
  const [videoFullscreen, setVideoFullscreen] = useState(false);

  const videoCurrentTime = useSharedValue(0);
  const isVideoFinished = useSharedValue(false);

  const inlineVideoPlayer = useVideoPlayer(null, (player) => {
    player.loop = false;
    player.playbackRate = 1;
    player.timeUpdateEventInterval = 0;
    player.preservesPitch = true;
  });

  const fullscreenVideoPlayer = useVideoPlayer(null, (player) => {
    player.loop = false;
    player.playbackRate = 1;
    player.timeUpdateEventInterval = 0;
    player.preservesPitch = true;
  });

  const currentVideoPlayer = useMemo(() => {
    return videoFullscreen ? fullscreenVideoPlayer : inlineVideoPlayer;
  }, [fullscreenVideoPlayer, videoFullscreen, inlineVideoPlayer]);

  const { isPlaying: inlineVideoPlaying } = useEvent(
    inlineVideoPlayer,
    'playingChange',
    {
      isPlaying: inlineVideoPlayer.playing,
    },
  );

  const { isPlaying: fullscreenVideoPlaying } = useEvent(
    fullscreenVideoPlayer,
    'playingChange',
    {
      isPlaying: fullscreenVideoPlayer.playing,
    },
  );

  const isVideoPlaying = useMemo(() => {
    return inlineVideoPlaying || fullscreenVideoPlaying;
  }, [fullscreenVideoPlaying, inlineVideoPlaying]);

  useEventListener(currentVideoPlayer, 'playbackRateChange', (payload) => {
    const { playbackRate } = payload;
    setVideoPlaybackRate(playbackRate);
  });

  useEventListener(currentVideoPlayer, 'timeUpdate', (payload) => {
    videoCurrentTime.set(payload.currentTime);
  });

  useEventListener(currentVideoPlayer, 'playToEnd', () => {
    if (currentVideoPlayer && currentVideoPlayer.currentTime >= 1) {
      isVideoFinished.value = true;
    }
  });

  useEffect(() => {
    console.log('royrao: 内联播放器状态变化', { inlineVideoPlaying });

    // 使用InteractionManager优化性能，避免在动画期间更新
    InteractionManager.runAfterInteractions(() => {
      if (inlineVideoPlaying) {
        inlineVideoPlayer.timeUpdateEventInterval = VIDEO_EVENT_UPDATE_TIME;
      } else {
        inlineVideoPlayer.timeUpdateEventInterval = 0;
      }
    });
  }, [inlineVideoPlayer, inlineVideoPlaying]);

  useEffect(() => {
    console.log('royrao: 全屏播放器状态变化', { fullscreenVideoPlaying });

    // 使用InteractionManager优化性能，避免在动画期间更新
    InteractionManager.runAfterInteractions(() => {
      if (fullscreenVideoPlaying) {
        fullscreenVideoPlayer.timeUpdateEventInterval = VIDEO_EVENT_UPDATE_TIME;
      } else {
        fullscreenVideoPlayer.timeUpdateEventInterval = 0;
      }
    });
  }, [fullscreenVideoPlayer, fullscreenVideoPlaying]);

  useAnimatedReaction(
    () => {
      return videoCurrentTime.value;
    },
    (currentTime) => {
      /* 当前时间低于阈值，说明视频刚开始播放（小于1s的视频看个屁） */
      if (currentTime < 1) {
        isVideoFinished.value = false;
      }
    },
  );

  return {
    inlineVideoPlayer,
    fullscreenVideoPlayer,
    isVideoPlaying,
    inlineVideoPlaying,
    fullscreenVideoPlaying,
    selectedVideoResource,
    videoPlaybackRate,
    currentVideoPlayer,
    videoFullscreen,
    videoCurrentTime,
    isVideoFinished,

    setSelectedVideoResource,
    setVideoFullscreen,
  };
};
