import { type MediaResourceServicePlayDTO } from '@jgl/biz-func';
import { showToast } from '@jgl/utils';
import { type VideoPlayer } from 'expo-video';
import { useCallback } from 'react';
import { InteractionManager } from 'react-native';
import { useSharedDraggableMenuContext } from '../../context/SharedDraggableMenuContext';

/**
 * 管理两个VideoPlayer之间的状态同步
 * - inlineVideoPlayer: 内联播放器
 * - fullscreenVideoPlayer: 全屏播放器
 */
export const useVideoPlayerSync = () => {
  const {
    inlineVideoPlayer,
    fullscreenVideoPlayer,
    selectedVideoResource,
    videoFullscreen,
    isVideoFinished,
    videoCurrentTime,
    setVideoFullscreen,
  } = useSharedDraggableMenuContext();

  const videoDuration = selectedVideoResource?.ext?.times || 0;

  // 同步两个播放器的状态
  const syncPlayerState = useCallback(
    async (fromPlayer: VideoPlayer, toPlayer: VideoPlayer) => {
      if (!fromPlayer || !toPlayer) return;

      try {
        // 确保目标播放器已准备好
        if (toPlayer.status !== 'readyToPlay' && toPlayer.status !== 'idle') {
          return;
        }

        // 同步播放进度和播放速率
        toPlayer.currentTime = fromPlayer.currentTime;
        toPlayer.playbackRate = fromPlayer.playbackRate;

        // 使用InteractionManager确保在合适的时机同步播放状态，避免阻塞UI
        InteractionManager.runAfterInteractions(() => {
          try {
            // 同步播放状态
            if (fromPlayer.playing) {
              toPlayer.play();
            } else {
              toPlayer.pause();
            }
          } catch (error) {
            console.error('royrao: 同步播放状态失败 - ', error);
          }
        });
      } catch (error) {
        console.error('royrao: 同步播放器状态失败 - ', error);
      }
    },
    [],
  );

  // 播放视频（两个播放器都加载相同内容）
  const playVideo = useCallback(
    async (resInfo: MediaResourceServicePlayDTO, autoPlay = true) => {
      console.log('royrao: 准备播放 - ', { autoPlay, resInfo });

      if (!resInfo?.url || !inlineVideoPlayer || !fullscreenVideoPlayer) {
        return;
      }

      try {
        // 两个播放器都加载相同的视频
        await Promise.all([
          inlineVideoPlayer.replaceAsync(resInfo.url),
          fullscreenVideoPlayer.replaceAsync(resInfo.url),
        ]);

        // 检查播放器状态
        if (
          inlineVideoPlayer.status === 'error' ||
          fullscreenVideoPlayer.status === 'error'
        ) {
          showToast({ title: '视频播放失败' });
          return;
        }

        // 同步初始状态
        InteractionManager.runAfterInteractions(() => {
          if (autoPlay) {
            if (videoFullscreen) {
              fullscreenVideoPlayer.replay();
              fullscreenVideoPlayer.play();
            } else {
              inlineVideoPlayer.replay();
              inlineVideoPlayer.play();
            }
          } else {
            inlineVideoPlayer.pause();
            fullscreenVideoPlayer.pause();
          }
        });
      } catch (error) {
        console.error('royrao: 播放视频失败 - ', error);
      }
    },
    [inlineVideoPlayer, fullscreenVideoPlayer, videoFullscreen],
  );

  // 暂停视频
  const pauseVideo = useCallback(() => {
    if (videoFullscreen) {
      fullscreenVideoPlayer?.pause();
    } else {
      inlineVideoPlayer?.pause();
    }
  }, [inlineVideoPlayer, fullscreenVideoPlayer, videoFullscreen]);

  // 播放/暂停切换
  const handlePressPlayOrPause = useCallback(() => {
    const currentPlayer = videoFullscreen
      ? fullscreenVideoPlayer
      : inlineVideoPlayer;

    if (!currentPlayer) {
      return;
    }

    if (currentPlayer.playing) {
      currentPlayer.pause();
    } else {
      if (isVideoFinished?.value) {
        currentPlayer.replay();
      }
      currentPlayer.play();
    }
  }, [
    isVideoFinished,
    inlineVideoPlayer,
    fullscreenVideoPlayer,
    videoFullscreen,
  ]);

  // 进入全屏
  const handleEnterFullscreen = useCallback(async () => {
    try {
      if (!inlineVideoPlayer || !fullscreenVideoPlayer) {
        return;
      }

      InteractionManager.runAfterInteractions(async () => {
        try {
          // 暂停内联播放器
          inlineVideoPlayer.pause();

          // 同步状态：从内联播放器到全屏播放器
          await syncPlayerState(inlineVideoPlayer, fullscreenVideoPlayer);

          // 显示全屏模态框
          setVideoFullscreen?.(true);

          setTimeout(() => {
            fullscreenVideoPlayer.play();
          }, 300);
        } catch (error) {
          console.error('royrao: 全屏状态同步失败 - ', error);
        }
      });
    } catch (error) {
      console.error('royrao: 进入全屏失败 - ', error);
    }
  }, [
    inlineVideoPlayer,
    fullscreenVideoPlayer,
    setVideoFullscreen,
    syncPlayerState,
  ]);

  // 退出全屏
  const handleExitFullscreen = useCallback(async () => {
    try {
      if (!inlineVideoPlayer || !fullscreenVideoPlayer) {
        return;
      }

      InteractionManager.runAfterInteractions(async () => {
        try {
          // 暂停全屏播放器
          fullscreenVideoPlayer.pause();

          // 立即关闭全屏模态框，不等待状态同步完成
          setVideoFullscreen?.(false);

          // 同步状态：从全屏播放器到内联播放器
          await syncPlayerState(fullscreenVideoPlayer, inlineVideoPlayer);

          setTimeout(() => {
            inlineVideoPlayer.play();
          }, 300);
        } catch (error) {
          console.error('royrao: 退出全屏状态同步失败 - ', error);
        }
      });
    } catch (error) {
      console.error('royrao: 退出全屏失败 - ', error);
    }
  }, [
    inlineVideoPlayer,
    fullscreenVideoPlayer,
    syncPlayerState,
    setVideoFullscreen,
  ]);

  // 跳转到指定时间
  const handleSeek = useCallback(
    (value: number) => {
      const seekTime = Math.max(
        0,
        Math.min((value / 100) * videoDuration, videoDuration),
      );

      const currentPlayer = videoFullscreen
        ? fullscreenVideoPlayer
        : inlineVideoPlayer;
      if (currentPlayer) {
        currentPlayer.currentTime = seekTime;

        if (videoCurrentTime !== undefined) {
          videoCurrentTime.value = seekTime;
        }
      }
    },
    [
      fullscreenVideoPlayer,
      inlineVideoPlayer,
      videoCurrentTime,
      videoDuration,
      videoFullscreen,
    ],
  );

  // 改变播放速率
  const handleChangePlaybackRate = useCallback(
    (rate: number) => {
      if (rate < 0 || rate > 16) {
        return;
      }

      const currentPlayer = videoFullscreen
        ? fullscreenVideoPlayer
        : inlineVideoPlayer;
      if (currentPlayer && rate !== currentPlayer.playbackRate) {
        currentPlayer.playbackRate = rate;
      }
    },
    [inlineVideoPlayer, fullscreenVideoPlayer, videoFullscreen],
  );

  return {
    videoDuration,
    playVideo,
    pauseVideo,
    handlePressPlayOrPause,
    handleEnterFullscreen,
    handleExitFullscreen,
    handleSeek,
    handleChangePlaybackRate,
  };
};
